{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/AutoWallpaper.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/AutoWallpaper.csproj"], "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/AutoWallpaper.csproj"], "problemMatcher": "$msCompile"}, {"label": "pack-msix", "command": "powershell", "type": "shell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/build.ps1", "-Pack"], "group": "build", "dependsOn": "build"}, {"label": "test-api", "command": "powershell", "type": "shell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/test.ps1", "-Api"], "group": "test"}]}