using AutoWallpaper.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 拾光壁纸 API 服务实现
/// </summary>
public class TimelineApiService : ITimelineApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<TimelineApiService> _logger;
    private readonly IConfigurationService _configService;
    
    private const string BaseUrl = "https://api.nguaduot.cn";
    
    public TimelineApiService(
        HttpClient httpClient, 
        ILogger<TimelineApiService> logger,
        IConfigurationService configService)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configService = configService;
    }

    public async Task<WallpaperImage?> GetRandomImageAsync(string provider = "snake", CancellationToken cancellationToken = default)
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var url = $"{BaseUrl}/{provider}/random?json=1";
            
            using var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Timeline-Client", config.Api.ApiKey);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var apiResponse = JsonSerializer.Deserialize<TimelineApiResponse<WallpaperImage>>(content);
            
            if (apiResponse?.IsSuccess == true && apiResponse.Data != null)
            {
                _logger.LogInformation("获取随机壁纸成功: {Title}", apiResponse.Data.Title);
                return apiResponse.Data;
            }
            
            _logger.LogWarning("API 返回失败: {Message}", apiResponse?.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取随机壁纸失败");
            return null;
        }
    }

    public async Task<WallpaperImage?> GetTodayImageAsync(string provider = "snake", CancellationToken cancellationToken = default)
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var url = $"{BaseUrl}/{provider}/today?json=1";
            
            using var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Timeline-Client", config.Api.ApiKey);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var apiResponse = JsonSerializer.Deserialize<TimelineApiResponse<WallpaperImage>>(content);
            
            if (apiResponse?.IsSuccess == true && apiResponse.Data != null)
            {
                _logger.LogInformation("获取今日壁纸成功: {Title}", apiResponse.Data.Title);
                return apiResponse.Data;
            }
            
            _logger.LogWarning("API 返回失败: {Message}", apiResponse?.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取今日壁纸失败");
            return null;
        }
    }

    public async Task<List<WallpaperImage>> GetImagesAsync(
        string provider = "snake",
        string order = "score",
        string categoryHow = "",
        string categoryWhat = "",
        int? no = null,
        string? seed = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var url = $"{BaseUrl}/{provider}/v4";
            
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(order)) queryParams.Add($"order={order}");
            if (!string.IsNullOrEmpty(categoryHow)) queryParams.Add($"catehow={categoryHow}");
            if (!string.IsNullOrEmpty(categoryWhat)) queryParams.Add($"catewhat={categoryWhat}");
            if (no.HasValue) queryParams.Add($"no={no.Value}");
            if (!string.IsNullOrEmpty(seed)) queryParams.Add($"seed={seed}");
            
            if (queryParams.Count > 0)
                url += "?" + string.Join("&", queryParams);
            
            using var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Timeline-Client", config.Api.ApiKey);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var apiResponse = JsonSerializer.Deserialize<TimelineApiResponse<List<WallpaperImage>>>(content);
            
            if (apiResponse?.IsSuccess == true && apiResponse.Data != null)
            {
                var filteredImages = apiResponse.Data
                    .Where(img => img.Width >= config.Download.MinWidth && 
                                 img.Height >= config.Download.MinHeight &&
                                 img.Size >= config.Download.MinFileSize)
                    .ToList();
                
                _logger.LogInformation("获取壁纸列表成功: {Count}/{Total} 张符合条件", 
                    filteredImages.Count, apiResponse.Data.Count);
                return filteredImages;
            }
            
            _logger.LogWarning("API 返回失败: {Message}", apiResponse?.Message);
            return new List<WallpaperImage>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取壁纸列表失败");
            return new List<WallpaperImage>();
        }
    }

    public async Task<List<CategoryInfo>> GetCategoriesAsync(string provider = "snake", string version = "v2", CancellationToken cancellationToken = default)
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var url = $"{BaseUrl}/{provider}/cate/{version}";
            
            using var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Timeline-Client", config.Api.ApiKey);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var apiResponse = JsonSerializer.Deserialize<TimelineApiResponse<List<CategoryInfo>>>(content);
            
            if (apiResponse?.IsSuccess == true && apiResponse.Data != null)
            {
                _logger.LogInformation("获取分类信息成功: {Count} 个分类", apiResponse.Data.Count);
                return apiResponse.Data;
            }
            
            _logger.LogWarning("API 返回失败: {Message}", apiResponse?.Message);
            return new List<CategoryInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类信息失败");
            return new List<CategoryInfo>();
        }
    }

    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var image = await GetRandomImageAsync("timeline", cancellationToken);
            return image != null;
        }
        catch
        {
            return false;
        }
    }
}
