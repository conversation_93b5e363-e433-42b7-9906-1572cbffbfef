using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.UI.Xaml;
using AutoWallpaper.Services;
using AutoWallpaper.ViewModels;
using System;

namespace AutoWallpaper;

public partial class App : Application
{
    private static IHost? _host;
    
    public static IHost Host => _host ?? throw new InvalidOperationException("Host not initialized");

    public App()
    {
        this.InitializeComponent();
        
        // 配置依赖注入和服务
        _host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 核心服务
                services.AddSingleton<ITimelineApiService, TimelineApiService>();
                services.AddSingleton<IImageDownloadService, ImageDownloadService>();
                services.AddSingleton<IWallpaperService, WallpaperService>();
                services.AddSingleton<ISchedulerService, SchedulerService>();
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                
                // ViewModels
                services.AddTransient<MainViewModel>();
                services.AddTransient<SettingsViewModel>();
                
                // HTTP Client
                services.AddHttpClient<ITimelineApiService, TimelineApiService>(client =>
                {
                    client.DefaultRequestHeaders.Add("Timeline-Client", "wer7qx"); // 测试密钥
                    client.Timeout = TimeSpan.FromSeconds(30);
                });
                
                // 日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            })
            .Build();
    }

    protected override void OnLaunched(Microsoft.UI.Xaml.LaunchActivatedEventArgs args)
    {
        m_window = new MainWindow();
        m_window.Activate();
    }

    private Window? m_window;
}
