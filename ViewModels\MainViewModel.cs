using AutoWallpaper.Models;
using AutoWallpaper.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace AutoWallpaper.ViewModels;

/// <summary>
/// 主页面 ViewModel
/// </summary>
public partial class MainViewModel : ObservableObject
{
    private readonly ISchedulerService _schedulerService;
    private readonly ITimelineApiService _apiService;
    private readonly IImageDownloadService _downloadService;
    private readonly IWallpaperService _wallpaperService;
    private readonly IConfigurationService _configService;
    private readonly ILogger<MainViewModel> _logger;

    [ObservableProperty]
    private bool _isScheduleRunning;

    [ObservableProperty]
    private DateTime? _nextChangeTime;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private bool _isChangingWallpaper;

    [ObservableProperty]
    private string? _currentImageTitle;

    [ObservableProperty]
    private string? _currentImagePath;

    [ObservableProperty]
    private CacheStats _cacheStats = new();

    public ObservableCollection<string> RecentChanges { get; } = new();

    public MainViewModel(
        ISchedulerService schedulerService,
        ITimelineApiService apiService,
        IImageDownloadService downloadService,
        IWallpaperService wallpaperService,
        IConfigurationService configService,
        ILogger<MainViewModel> logger)
    {
        _schedulerService = schedulerService;
        _apiService = apiService;
        _downloadService = downloadService;
        _wallpaperService = wallpaperService;
        _configService = configService;
        _logger = logger;

        // 订阅事件
        _schedulerService.StatusChanged += OnScheduleStatusChanged;
        _schedulerService.WallpaperChanged += OnWallpaperChanged;
    }

    [RelayCommand]
    private async Task InitializeAsync()
    {
        try
        {
            StatusMessage = "初始化中...";
            
            // 加载缓存统计
            CacheStats = await _downloadService.GetCacheStatsAsync();
            
            // 检查调度状态
            IsScheduleRunning = _schedulerService.IsRunning;
            NextChangeTime = _schedulerService.NextExecutionTime;
            
            // 获取当前壁纸信息
            CurrentImagePath = await _wallpaperService.GetCurrentDesktopWallpaperAsync();
            
            StatusMessage = "就绪";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化失败");
            StatusMessage = "初始化失败";
        }
    }

    [RelayCommand]
    private async Task ChangeWallpaperNowAsync()
    {
        if (IsChangingWallpaper) return;

        try
        {
            IsChangingWallpaper = true;
            StatusMessage = "正在更换壁纸...";
            
            await _schedulerService.TriggerWallpaperChangeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动更换壁纸失败");
            StatusMessage = "更换失败";
        }
        finally
        {
            IsChangingWallpaper = false;
        }
    }

    [RelayCommand]
    private async Task ToggleScheduleAsync()
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            
            if (IsScheduleRunning)
            {
                await _schedulerService.StopScheduleAsync();
                StatusMessage = "定时任务已停止";
            }
            else
            {
                await _schedulerService.StartScheduleAsync(config.Schedule.IntervalMinutes);
                StatusMessage = $"定时任务已启动，间隔 {config.Schedule.IntervalMinutes} 分钟";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换定时任务状态失败");
            StatusMessage = "操作失败";
        }
    }

    [RelayCommand]
    private async Task RefreshCacheStatsAsync()
    {
        try
        {
            CacheStats = await _downloadService.GetCacheStatsAsync();
            StatusMessage = "缓存信息已刷新";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新缓存统计失败");
            StatusMessage = "刷新失败";
        }
    }

    [RelayCommand]
    private async Task CleanupCacheAsync()
    {
        try
        {
            StatusMessage = "正在清理缓存...";
            await _downloadService.CleanupCacheAsync();
            CacheStats = await _downloadService.GetCacheStatsAsync();
            StatusMessage = "缓存清理完成";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存失败");
            StatusMessage = "清理失败";
        }
    }

    private void OnScheduleStatusChanged(object? sender, ScheduleStatusChangedEventArgs e)
    {
        IsScheduleRunning = e.IsRunning;
        NextChangeTime = e.NextExecutionTime;
        
        if (!string.IsNullOrEmpty(e.Message))
        {
            StatusMessage = e.Message;
        }
    }

    private void OnWallpaperChanged(object? sender, WallpaperChangedEventArgs e)
    {
        IsChangingWallpaper = false;
        
        if (e.Success)
        {
            CurrentImageTitle = e.ImageTitle;
            CurrentImagePath = e.ImagePath;
            StatusMessage = $"壁纸更换成功: {e.ImageTitle}";
            
            // 添加到最近更换记录
            var changeRecord = $"{DateTime.Now:HH:mm} - {e.ImageTitle}";
            RecentChanges.Insert(0, changeRecord);
            
            // 保持最近 10 条记录
            while (RecentChanges.Count > 10)
            {
                RecentChanges.RemoveAt(RecentChanges.Count - 1);
            }
        }
        else
        {
            StatusMessage = $"壁纸更换失败: {e.Error}";
        }
    }
}
