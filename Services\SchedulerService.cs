using AutoWallpaper.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 调度服务实现
/// </summary>
public class SchedulerService : ISchedulerService, IDisposable
{
    private readonly ILogger<SchedulerService> _logger;
    private readonly ITimelineApiService _apiService;
    private readonly IImageDownloadService _downloadService;
    private readonly IWallpaperService _wallpaperService;
    private readonly IConfigurationService _configService;
    
    private Timer? _timer;
    private bool _isRunning;
    private DateTime? _nextExecutionTime;
    private readonly object _lock = new();

    public bool IsRunning 
    { 
        get 
        { 
            lock (_lock) 
            { 
                return _isRunning; 
            } 
        } 
    }

    public DateTime? NextExecutionTime 
    { 
        get 
        { 
            lock (_lock) 
            { 
                return _nextExecutionTime; 
            } 
        } 
    }

    public event EventHandler<ScheduleStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<WallpaperChangedEventArgs>? WallpaperChanged;

    public SchedulerService(
        ILogger<SchedulerService> logger,
        ITimelineApiService apiService,
        IImageDownloadService downloadService,
        IWallpaperService wallpaperService,
        IConfigurationService configService)
    {
        _logger = logger;
        _apiService = apiService;
        _downloadService = downloadService;
        _wallpaperService = wallpaperService;
        _configService = configService;
    }

    public async Task StartScheduleAsync(int intervalMinutes)
    {
        if (intervalMinutes < 30)
        {
            throw new ArgumentException("更换间隔不能少于 30 分钟", nameof(intervalMinutes));
        }

        await StopScheduleAsync();

        lock (_lock)
        {
            _isRunning = true;
            _nextExecutionTime = DateTime.Now.AddMinutes(intervalMinutes);
        }

        var interval = TimeSpan.FromMinutes(intervalMinutes);
        _timer = new Timer(OnTimerElapsed, null, interval, interval);
        
        _logger.LogInformation("定时任务已启动，间隔: {Interval} 分钟", intervalMinutes);
        
        StatusChanged?.Invoke(this, new ScheduleStatusChangedEventArgs
        {
            IsRunning = true,
            NextExecutionTime = _nextExecutionTime,
            Message = $"定时任务已启动，间隔 {intervalMinutes} 分钟"
        });

        // 立即执行一次
        _ = Task.Run(async () => await TriggerWallpaperChangeAsync());
    }

    public async Task StopScheduleAsync()
    {
        lock (_lock)
        {
            _isRunning = false;
            _nextExecutionTime = null;
        }

        _timer?.Dispose();
        _timer = null;
        
        _logger.LogInformation("定时任务已停止");
        
        StatusChanged?.Invoke(this, new ScheduleStatusChangedEventArgs
        {
            IsRunning = false,
            NextExecutionTime = null,
            Message = "定时任务已停止"
        });

        await Task.CompletedTask;
    }

    public async Task TriggerWallpaperChangeAsync()
    {
        try
        {
            _logger.LogInformation("开始更换壁纸...");
            
            var config = await _configService.GetConfigAsync();
            
            // 获取壁纸
            WallpaperImage? image = null;
            
            // 根据配置选择获取方式
            switch (config.Api.Order.ToLowerInvariant())
            {
                case "random":
                    image = await _apiService.GetRandomImageAsync(config.Api.Provider);
                    break;
                case "today":
                    image = await _apiService.GetTodayImageAsync(config.Api.Provider);
                    break;
                default:
                    var images = await _apiService.GetImagesAsync(
                        config.Api.Provider,
                        config.Api.Order,
                        config.Api.CategoryHow,
                        config.Api.CategoryWhat);
                    image = images.FirstOrDefault();
                    break;
            }

            if (image == null)
            {
                var error = "未能获取到壁纸";
                _logger.LogWarning(error);
                WallpaperChanged?.Invoke(this, new WallpaperChangedEventArgs
                {
                    Success = false,
                    Error = error
                });
                return;
            }

            // 下载图片
            var imagePath = await _downloadService.DownloadImageAsync(image);
            if (string.IsNullOrEmpty(imagePath))
            {
                var error = "图片下载失败";
                _logger.LogWarning(error);
                WallpaperChanged?.Invoke(this, new WallpaperChangedEventArgs
                {
                    Success = false,
                    Error = error,
                    ImageTitle = image.Title
                });
                return;
            }

            // 设置壁纸
            WallpaperSetResult result;
            if (config.Wallpaper.Target == WallpaperTarget.Desktop)
            {
                var success = await _wallpaperService.SetDesktopWallpaperAsync(imagePath, config.Wallpaper.DesktopStyle);
                result = new WallpaperSetResult { DesktopSuccess = success, LockScreenSuccess = true };
            }
            else if (config.Wallpaper.Target == WallpaperTarget.LockScreen)
            {
                var success = await _wallpaperService.SetLockScreenWallpaperAsync(imagePath);
                result = new WallpaperSetResult { DesktopSuccess = true, LockScreenSuccess = success };
            }
            else
            {
                result = await _wallpaperService.SetBothWallpapersAsync(imagePath, config.Wallpaper.DesktopStyle);
            }

            // 清理缓存
            await _downloadService.CleanupCacheAsync();

            WallpaperChanged?.Invoke(this, new WallpaperChangedEventArgs
            {
                Success = result.HasAnySuccess,
                ImagePath = imagePath,
                ImageTitle = image.Title,
                Error = result.HasAnySuccess ? null : "壁纸设置失败"
            });

            _logger.LogInformation("壁纸更换完成: {Title}", image.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "壁纸更换异常");
            WallpaperChanged?.Invoke(this, new WallpaperChangedEventArgs
            {
                Success = false,
                Error = ex.Message
            });
        }
    }

    private async void OnTimerElapsed(object? state)
    {
        lock (_lock)
        {
            if (!_isRunning) return;
            
            var config = _configService.GetConfigAsync().Result;
            _nextExecutionTime = DateTime.Now.AddMinutes(config.Schedule.IntervalMinutes);
        }

        await TriggerWallpaperChangeAsync();
        
        StatusChanged?.Invoke(this, new ScheduleStatusChangedEventArgs
        {
            IsRunning = true,
            NextExecutionTime = _nextExecutionTime,
            Message = "壁纸更换完成"
        });
    }

    public void Dispose()
    {
        _timer?.Dispose();
        GC.SuppressFinalize(this);
    }
}
