<Application
    x:Class="AutoWallpaper.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:AutoWallpaper"
    xmlns:converters="using:AutoWallpaper.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
                <!-- Other merged dictionaries here -->
            </ResourceDictionary.MergedDictionaries>
            <!-- Other app resources here -->
            
            <!-- 应用主题色 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#FF6B73FF"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FF9AA0FF"/>
            
            <!-- 卡片样式 -->
            <Style x:Key="CardStyle" TargetType="Border">
                <Setter Property="Background" Value="{ThemeResource CardBackgroundFillColorDefaultBrush}"/>
                <Setter Property="BorderBrush" Value="{ThemeResource CardStrokeColorDefaultBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
            </Style>

            <!-- 转换器 -->
            <converters:BoolNegationConverter x:Key="BoolNegationConverter"/>
            <converters:BoolToTextConverter x:Key="BoolToTextConverter"/>
            <converters:BoolToSymbolConverter x:Key="BoolToSymbolConverter"/>
            <converters:BoolToGlyphConverter x:Key="BoolToGlyphConverter"/>
            <converters:BoolToBrushConverter x:Key="BoolToBrushConverter"/>
            <converters:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
            <converters:IntToVisibilityConverter x:Key="IntToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
