<Page x:Class="AutoWallpaper.Views.PreviewPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="24">
            
            <TextBlock Text="壁纸预览" Style="{StaticResource TitleTextBlockStyle}"/>
            
            <!-- 预览功能占位 -->
            <Border Style="{StaticResource CardStyle}" MinHeight="400">
                <StackPanel Spacing="16" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <SymbolIcon Symbol="Pictures" FontSize="48" Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="壁纸预览功能" 
                              Style="{StaticResource SubtitleTextBlockStyle}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="此功能将在后续版本中实现" 
                              Style="{StaticResource BodyTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="• 浏览最新壁纸" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="• 预览壁纸效果" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="• 手动选择喜欢的壁纸" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</Page>
