# 自动壁纸 - Windows 11 壁纸自动更换应用

一个基于 C# WinUI 3 开发的 Windows 11 自动壁纸更换应用，从拾光壁纸网站获取高质量壁纸并自动设置为桌面背景和锁屏壁纸。

## 功能特性

- ✅ **多图源支持** - 支持拾光壁纸、必应、Unsplash、WallHaven 等多个图源
- ✅ **智能下载** - 自动下载高质量壁纸，支持本地缓存管理
- ✅ **桌面壁纸** - 完美支持 Windows 11 桌面壁纸设置，多种显示样式
- ✅ **锁屏壁纸** - 使用 Windows App SDK API 设置锁屏壁纸（需系统支持）
- ✅ **定时更换** - 可配置的定时更换功能，最小间隔 30 分钟
- ✅ **分类筛选** - 支持按摄影类型、主题分类筛选壁纸
- ✅ **现代界面** - 基于 WinUI 3 的现代化用户界面

## 系统要求

- Windows 11 (推荐) 或 Windows 10 版本 1903 及以上
- .NET 8.0 运行时
- 网络连接（用于获取壁纸）

## 快速开始

### 开发环境

1. **安装 Visual Studio 2022** (17.8 或更高版本)
   - 工作负载：".NET 桌面开发" 和 "通用 Windows 平台开发"
   - 组件：Windows App SDK、WinUI 3 项目模板

2. **克隆项目**
   ```bash
   git clone <repository-url>
   cd auto-wallpaper
   ```

3. **还原依赖**
   ```bash
   dotnet restore
   ```

4. **运行项目**
   ```bash
   dotnet run
   ```

### 用户使用

1. **下载安装包** - 从 Releases 页面下载最新的 MSIX 安装包
2. **安装应用** - 双击 MSIX 文件安装（可能需要启用开发者模式）
3. **配置 API** - 首次运行时配置 API 密钥（默认提供测试密钥）
4. **开始使用** - 点击"立即更换"测试功能，或启用定时更换

## 配置说明

### API 配置
- **API 密钥**: 拾光壁纸 API 授权密钥（默认提供测试密钥 `wer7qx`）
- **图源选择**: 支持故纸堆（聚合）、周度精选、拾光、必应等
- **排序方式**: 趋势、最新、随缘
- **分类筛选**: 可按摄影/其他大类，风光/人物/美女等子类筛选

### 壁纸配置
- **设置目标**: 桌面壁纸、锁屏壁纸或两者
- **桌面样式**: 填充、适应、拉伸、居中、平铺、跨越
- **质量要求**: 最小分辨率 1920x1080，文件大小 100KB 以上

### 定时配置
- **更换间隔**: 30 分钟到任意时长
- **空闲检测**: 可选择仅在系统空闲时更换

## 技术架构

### 核心组件
- **TimelineApiService** - 拾光壁纸 API 客户端
- **ImageDownloadService** - 图片下载和缓存管理
- **WallpaperService** - 壁纸设置（桌面 + 锁屏）
- **SchedulerService** - 定时任务调度
- **ConfigurationService** - 配置管理和持久化

### 技术栈
- **框架**: .NET 8.0 + WinUI 3 + Windows App SDK
- **架构**: MVVM + 依赖注入
- **UI**: WinUI 3 + CommunityToolkit.Mvvm
- **HTTP**: HttpClient + System.Text.Json
- **打包**: MSIX (Windows App SDK)

## 开发指南

### 项目结构
```
AutoWallpaper/
├── Models/           # 数据模型
├── Services/         # 核心服务
├── ViewModels/       # MVVM 视图模型
├── Views/           # WinUI 3 页面
├── Converters/      # XAML 转换器
├── Assets/          # 应用图标和资源
└── Package.appxmanifest  # 应用清单
```

### 添加新功能
1. 在 `Services/` 中定义接口和实现
2. 在 `App.xaml.cs` 中注册服务
3. 在 `ViewModels/` 中创建视图模型
4. 在 `Views/` 中创建对应页面

## 常见问题

### 锁屏壁纸设置失败
- **原因**: Windows 系统策略限制或权限不足
- **解决**: 确保应用已正确打包为 MSIX，并检查系统个性化设置权限

### API 连接失败
- **原因**: 网络问题或 API 密钥失效
- **解决**: 检查网络连接，联系拾光壁纸申请正式 API 密钥

### 图片下载缓慢
- **原因**: 网络速度或图片服务器响应慢
- **解决**: 调整并发下载数，或选择其他图源

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 致谢

- [拾光壁纸](https://snake.timeline.ink/) - 提供优质的壁纸 API 服务
- [Windows App SDK](https://docs.microsoft.com/windows/apps/windows-app-sdk/) - 现代 Windows 应用开发框架
- [CommunityToolkit.Mvvm](https://docs.microsoft.com/dotnet/communitytoolkit/mvvm/) - MVVM 工具包
