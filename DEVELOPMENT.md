# 开发指南

## 项目概述

这是一个基于 C# WinUI 3 和 Windows App SDK 开发的 Windows 11 自动壁纸更换应用。

## 开发环境设置

### 必需软件

1. **Visual Studio 2022** (版本 17.8 或更高)
   - 工作负载：
     - .NET 桌面开发
     - 通用 Windows 平台开发
   - 组件：
     - Windows App SDK
     - WinUI 3 项目模板

2. **.NET 8.0 SDK**
   - 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0

3. **Windows 11 SDK** (可选，Visual Studio 会自动安装)

### 开发者模式

在 Windows 设置中启用开发者模式：
1. 打开 Windows 设置
2. 转到 "隐私和安全性" > "开发者选项"
3. 启用 "开发者模式"

## 快速开始

### 1. 克隆和构建

```powershell
# 克隆项目（如果从 Git）
git clone <repository-url>
cd auto-wallpaper

# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行应用
dotnet run
```

### 2. 使用构建脚本

```powershell
# 基础构建
.\build.ps1

# 清理并构建
.\build.ps1 -Clean

# 构建并打包 MSIX
.\build.ps1 -Pack

# 构建、打包并安装
.\build.ps1 -Pack -Install
```

### 3. 测试功能

```powershell
# 运行所有测试
.\test.ps1 -All

# 仅测试 API
.\test.ps1 -Api

# 仅测试下载
.\test.ps1 -Download
```

## 项目结构详解

```
AutoWallpaper/
├── Models/                 # 数据模型
│   ├── WallpaperImage.cs  # 壁纸图片模型
│   └── AppConfig.cs       # 应用配置模型
├── Services/              # 核心服务
│   ├── ITimelineApiService.cs      # API 服务接口
│   ├── TimelineApiService.cs       # API 服务实现
│   ├── IImageDownloadService.cs    # 下载服务接口
│   ├── ImageDownloadService.cs     # 下载服务实现
│   ├── IWallpaperService.cs        # 壁纸服务接口
│   ├── WallpaperService.cs         # 壁纸服务实现
│   ├── ISchedulerService.cs        # 调度服务接口
│   ├── SchedulerService.cs         # 调度服务实现
│   ├── IConfigurationService.cs    # 配置服务接口
│   └── ConfigurationService.cs     # 配置服务实现
├── ViewModels/            # MVVM 视图模型
│   ├── MainViewModel.cs   # 主页视图模型
│   └── SettingsViewModel.cs # 设置页视图模型
├── Views/                 # WinUI 3 页面
│   ├── HomePage.xaml      # 主页
│   ├── SettingsPage.xaml  # 设置页
│   ├── PreviewPage.xaml   # 预览页（占位）
│   └── SchedulePage.xaml  # 调度页（占位）
├── Converters/            # XAML 转换器
│   └── CommonConverters.cs # 通用转换器
├── Assets/                # 应用资源
│   └── README.md          # 图标说明
├── AutoWallpaper.csproj   # 项目文件
├── Package.appxmanifest   # 应用清单
├── App.xaml              # 应用资源
├── MainWindow.xaml       # 主窗口
├── Program.cs            # 程序入口
├── build.ps1             # 构建脚本
├── test.ps1              # 测试脚本
└── README.md             # 项目说明
```

## 核心功能实现

### 1. API 客户端 (TimelineApiService)

- 支持多种图源（snake、timeline、bing 等）
- 实现授权头管理
- 支持分页、分类筛选
- 错误重试机制

### 2. 图片下载 (ImageDownloadService)

- 并发下载控制
- MD5 校验确保完整性
- 本地缓存管理
- 自动清理过期文件

### 3. 壁纸设置 (WallpaperService)

- **桌面壁纸**: 使用 Win32 API (SystemParametersInfoW)
- **锁屏壁纸**: 使用 Windows App SDK API (UserProfilePersonalizationSettings)
- 支持多种显示样式
- 错误处理和回退方案

### 4. 定时调度 (SchedulerService)

- 基于 Timer 的定时执行
- 最小间隔 30 分钟
- 事件通知机制
- 手动触发支持

## 调试技巧

### 1. 日志查看

应用使用 Microsoft.Extensions.Logging，日志会输出到：
- Visual Studio 输出窗口
- 控制台（命令行模式）

### 2. 断点调试

在 Visual Studio 中设置断点：
- API 调用: `TimelineApiService.GetRandomImageAsync`
- 下载过程: `ImageDownloadService.DownloadImageAsync`
- 壁纸设置: `WallpaperService.SetDesktopWallpaperAsync`

### 3. 命令行测试

```powershell
# 测试 API 连接
dotnet run -- --test-api

# 立即更换壁纸
dotnet run -- --change-now

# 启动 30 分钟定时任务
dotnet run -- --start-schedule 30
```

## 常见问题

### 1. 编译错误

**问题**: 缺少 Windows App SDK 引用
**解决**: 确保安装了最新的 Visual Studio 和 Windows App SDK

**问题**: WinUI 3 模板缺失
**解决**: 在 Visual Studio Installer 中安装 "Windows App SDK" 组件

### 2. 运行时错误

**问题**: 锁屏壁纸设置失败
**解决**: 
- 确保应用已打包为 MSIX
- 检查系统个性化设置权限
- 在 Windows 设置中允许应用修改锁屏

**问题**: API 授权失败
**解决**: 
- 检查网络连接
- 验证 API 密钥是否有效
- 联系拾光壁纸申请正式密钥

### 3. 打包问题

**问题**: MSIX 打包失败
**解决**: 
- 确保所有资源文件存在
- 检查应用清单配置
- 使用 Visual Studio 的打包向导

## 扩展开发

### 添加新图源

1. 在 `TimelineApiService` 中添加新的 API 端点
2. 在 `SettingsViewModel` 中添加图源选项
3. 更新配置模型支持新参数

### 添加新功能页面

1. 在 `Views/` 中创建新的 XAML 页面
2. 在 `ViewModels/` 中创建对应的视图模型
3. 在 `MainWindow.xaml` 中添加导航项
4. 在 `App.xaml.cs` 中注册相关服务

### 自定义壁纸处理

1. 在 `WallpaperService` 中添加图片处理逻辑
2. 支持裁剪、缩放、滤镜等功能
3. 添加多显示器支持

## 部署指南

### 开发部署

1. 使用 Visual Studio 直接运行调试
2. 使用 `dotnet run` 命令行启动

### 生产部署

1. 构建 Release 版本
2. 打包为 MSIX
3. 可选：代码签名
4. 分发安装包

### 自动部署

可以配置 GitHub Actions 或 Azure DevOps 进行自动构建和打包。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License - 详见 LICENSE 文件
