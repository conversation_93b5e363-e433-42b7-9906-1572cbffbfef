using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using AutoWallpaper.Views;

namespace AutoWallpaper;

public sealed partial class MainWindow : Window
{
    public MainWindow()
    {
        this.InitializeComponent();
        
        // 设置窗口标题和大小
        Title = "自动壁纸";
        this.AppWindow.Resize(new Windows.Graphics.SizeInt32(1200, 800));
        
        // 默认导航到主页
        ContentFrame.Navigate(typeof(HomePage));
        NavView.SelectedItem = NavView.MenuItems[0];
    }

    private void NavView_SelectionChanged(NavigationView sender, NavigationViewSelectionChangedEventArgs args)
    {
        if (args.IsSettingsSelected)
        {
            ContentFrame.Navigate(typeof(SettingsPage));
        }
        else if (args.SelectedItem is NavigationViewItem item)
        {
            var tag = item.Tag?.ToString();
            switch (tag)
            {
                case "Home":
                    ContentFrame.Navigate(typeof(HomePage));
                    break;
                case "Preview":
                    ContentFrame.Navigate(typeof(PreviewPage));
                    break;
                case "Schedule":
                    ContentFrame.Navigate(typeof(SchedulePage));
                    break;
            }
        }
    }
}
