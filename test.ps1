# 自动壁纸应用测试脚本

param(
    [switch]$Api,
    [switch]$Download,
    [switch]$Wallpaper,
    [switch]$All
)

$ErrorActionPreference = "Stop"

Write-Host "=== 自动壁纸应用测试 ===" -ForegroundColor Green

if ($All) {
    $Api = $true
    $Download = $true
    $Wallpaper = $true
}

# 测试 API 连接
if ($Api) {
    Write-Host "`n测试 API 连接..." -ForegroundColor Yellow
    
    try {
        $headers = @{ "Timeline-Client" = "wer7qx" }
        $response = Invoke-RestMethod -Uri "https://api.nguaduot.cn/timeline/random?json=1" -Headers $headers -TimeoutSec 10
        
        if ($response.status -eq 1 -and $response.data) {
            Write-Host "✓ API 连接成功" -ForegroundColor Green
            Write-Host "  图片标题: $($response.data.title)" -ForegroundColor Cyan
            Write-Host "  图片尺寸: $($response.data.width)x$($response.data.height)" -ForegroundColor Cyan
            Write-Host "  图片大小: $([math]::Round($response.data.size / 1024 / 1024, 2)) MB" -ForegroundColor Cyan
        }
        else {
            Write-Host "✗ API 返回异常: $($response.msg)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ API 连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试图片下载
if ($Download) {
    Write-Host "`n测试图片下载..." -ForegroundColor Yellow
    
    try {
        $headers = @{ "Timeline-Client" = "wer7qx" }
        $response = Invoke-RestMethod -Uri "https://api.nguaduot.cn/timeline/random?json=1" -Headers $headers -TimeoutSec 10
        
        if ($response.status -eq 1 -and $response.data -and $response.data.imgurl) {
            $imageUrl = $response.data.imgurl
            $fileName = "$($response.data.md5)$($response.data.ext)"
            $downloadPath = "$env:TEMP\AutoWallpaperTest"
            
            if (-not (Test-Path $downloadPath)) {
                New-Item -Path $downloadPath -ItemType Directory -Force | Out-Null
            }
            
            $filePath = Join-Path $downloadPath $fileName
            
            Write-Host "  下载地址: $imageUrl" -ForegroundColor Cyan
            Write-Host "  保存路径: $filePath" -ForegroundColor Cyan
            
            Invoke-WebRequest -Uri $imageUrl -OutFile $filePath -TimeoutSec 30
            
            if (Test-Path $filePath) {
                $fileInfo = Get-Item $filePath
                Write-Host "✓ 图片下载成功" -ForegroundColor Green
                Write-Host "  文件大小: $([math]::Round($fileInfo.Length / 1024 / 1024, 2)) MB" -ForegroundColor Cyan
                
                # 验证 MD5（简单检查）
                if ($fileInfo.Length -gt 50000) {
                    Write-Host "✓ 文件大小验证通过" -ForegroundColor Green
                }
                else {
                    Write-Host "⚠ 文件可能不完整" -ForegroundColor Yellow
                }
            }
            else {
                Write-Host "✗ 图片下载失败" -ForegroundColor Red
            }
        }
        else {
            Write-Host "✗ 无法获取图片信息" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ 下载测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试壁纸设置
if ($Wallpaper) {
    Write-Host "`n测试壁纸设置..." -ForegroundColor Yellow
    
    try {
        # 获取当前壁纸
        $currentWallpaper = Get-ItemProperty -Path "HKCU:\Control Panel\Desktop" -Name "Wallpaper" -ErrorAction SilentlyContinue
        if ($currentWallpaper) {
            Write-Host "  当前壁纸: $($currentWallpaper.Wallpaper)" -ForegroundColor Cyan
        }
        
        # 检查锁屏设置支持
        Write-Host "  检查锁屏 API 支持..." -ForegroundColor Cyan
        
        # 这里需要 C# 代码来检查，PowerShell 无法直接调用 WinRT API
        Write-Host "  锁屏 API 检查需要在 C# 应用中进行" -ForegroundColor Yellow
        
        Write-Host "✓ 壁纸设置功能就绪" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ 壁纸设置测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
Write-Host "使用 'dotnet run' 启动应用程序" -ForegroundColor Yellow
