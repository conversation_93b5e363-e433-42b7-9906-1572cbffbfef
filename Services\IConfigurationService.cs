using AutoWallpaper.Models;
using System;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取应用配置
    /// </summary>
    Task<AppConfig> GetConfigAsync();

    /// <summary>
    /// 保存应用配置
    /// </summary>
    Task SaveConfigAsync(AppConfig config);

    /// <summary>
    /// 重置为默认配置
    /// </summary>
    Task ResetToDefaultAsync();

    /// <summary>
    /// 配置变化事件
    /// </summary>
    event EventHandler<ConfigChangedEventArgs>? ConfigChanged;
}

/// <summary>
/// 配置变化事件参数
/// </summary>
public class ConfigChangedEventArgs : EventArgs
{
    public AppConfig Config { get; set; } = new();
    public string? ChangedSection { get; set; }
}
