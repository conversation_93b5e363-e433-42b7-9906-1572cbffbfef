using AutoWallpaper.Models;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 壁纸设置服务接口
/// </summary>
public interface IWallpaperService
{
    /// <summary>
    /// 设置桌面壁纸
    /// </summary>
    Task<bool> SetDesktopWallpaperAsync(string imagePath, WallpaperStyle style = WallpaperStyle.Fill);

    /// <summary>
    /// 设置锁屏壁纸
    /// </summary>
    Task<bool> SetLockScreenWallpaperAsync(string imagePath);

    /// <summary>
    /// 同时设置桌面和锁屏壁纸
    /// </summary>
    Task<WallpaperSetResult> SetBothWallpapersAsync(string imagePath, WallpaperStyle style = WallpaperStyle.Fill);

    /// <summary>
    /// 检查锁屏壁纸设置是否支持
    /// </summary>
    Task<bool> IsLockScreenSupportedAsync();

    /// <summary>
    /// 获取当前桌面壁纸路径
    /// </summary>
    Task<string?> GetCurrentDesktopWallpaperAsync();

    /// <summary>
    /// 获取当前锁屏壁纸路径
    /// </summary>
    Task<string?> GetCurrentLockScreenWallpaperAsync();
}

/// <summary>
/// 壁纸设置结果
/// </summary>
public class WallpaperSetResult
{
    public bool DesktopSuccess { get; set; }
    public bool LockScreenSuccess { get; set; }
    public string? DesktopError { get; set; }
    public string? LockScreenError { get; set; }
    
    public bool IsFullSuccess => DesktopSuccess && LockScreenSuccess;
    public bool HasAnySuccess => DesktopSuccess || LockScreenSuccess;
}
