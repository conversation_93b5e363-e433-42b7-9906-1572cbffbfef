<Window x:Class="AutoWallpaper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="using:AutoWallpaper">

    <Grid>
        <NavigationView x:Name="NavView" 
                       IsBackButtonVisible="Collapsed"
                       IsSettingsVisible="True"
                       PaneDisplayMode="Left"
                       SelectionChanged="NavView_SelectionChanged">
            
            <NavigationView.MenuItems>
                <NavigationViewItem Icon="Home" Content="主页" Tag="Home"/>
                <NavigationViewItem Icon="Pictures" Content="壁纸预览" Tag="Preview"/>
                <NavigationViewItem Icon="Clock" Content="定时设置" Tag="Schedule"/>
            </NavigationView.MenuItems>
            
            <Frame x:Name="ContentFrame"/>
        </NavigationView>
    </Grid>
</Window>
