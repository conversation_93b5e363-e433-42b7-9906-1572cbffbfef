# 安装指南

## 系统要求

- **操作系统**: Windows 11 (推荐) 或 Windows 10 版本 1903 及以上
- **架构**: x64, x86, 或 ARM64
- **网络**: 需要互联网连接获取壁纸
- **权限**: 标准用户权限（锁屏功能可能需要额外权限）

## 开发环境安装

### 1. 安装 Visual Studio 2022

下载并安装 Visual Studio 2022 Community（免费）或更高版本：
- 下载地址: https://visualstudio.microsoft.com/downloads/

**必需工作负载**:
- ✅ .NET 桌面开发
- ✅ 通用 Windows 平台开发

**必需组件**:
- ✅ Windows App SDK
- ✅ WinUI 3 项目模板
- ✅ Windows 11 SDK (10.0.22621.0 或更高)

### 2. 安装 .NET 8.0 SDK

如果 Visual Studio 没有自动安装：
- 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
- 选择 "SDK" 版本下载安装

### 3. 启用开发者模式

在 Windows 设置中启用开发者模式：
1. 打开 **Windows 设置** (Win + I)
2. 转到 **隐私和安全性** > **开发者选项**
3. 启用 **开发者模式**
4. 确认安全提示

## 项目构建

### 方法 1: Visual Studio

1. 打开 Visual Studio 2022
2. 选择 **打开项目或解决方案**
3. 选择 `AutoWallpaper.csproj` 文件
4. 等待项目加载和 NuGet 包还原
5. 按 **F5** 运行调试，或 **Ctrl+F5** 运行发布

### 方法 2: 命令行

```powershell
# 进入项目目录
cd auto-wallpaper

# 还原 NuGet 包
dotnet restore

# 构建项目
dotnet build --configuration Release

# 运行应用
dotnet run --configuration Release
```

### 方法 3: 构建脚本

```powershell
# 基础构建
.\build.ps1

# 清理并重新构建
.\build.ps1 -Clean

# 构建并打包 MSIX
.\build.ps1 -Pack

# 构建、打包并安装
.\build.ps1 -Pack -Install
```

## MSIX 打包和分发

### 打包应用

1. **使用 Visual Studio**:
   - 右键点击项目 → **打包和发布** → **创建应用包**
   - 选择 **旁加载** 或 **Microsoft Store**
   - 按向导完成打包

2. **使用命令行**:
   ```powershell
   .\build.ps1 -Pack
   ```

### 安装 MSIX 包

1. **双击安装** (推荐):
   - 双击生成的 `.msix` 文件
   - 点击 **安装** 按钮
   - 等待安装完成

2. **PowerShell 安装**:
   ```powershell
   Add-AppxPackage -Path "path\to\AutoWallpaper.msix"
   ```

3. **应用安装器**:
   - 如果双击无效，可能需要安装 "应用安装器"
   - 从 Microsoft Store 搜索并安装 "App Installer"

## 首次运行配置

### 1. API 密钥配置

应用默认使用测试密钥 `wer7qx`，但此密钥可能随时失效。

**获取正式密钥**:
1. 访问 https://doc.timeline.ink/about.html
2. 联系开发者申请 API 密钥
3. 在应用设置中更新密钥

### 2. 基础设置

首次运行时建议配置：
- ✅ 测试 API 连接
- ✅ 选择喜欢的图源和分类
- ✅ 设置下载路径
- ✅ 配置更换间隔（最小 30 分钟）
- ✅ 选择壁纸目标（桌面/锁屏/两者）

### 3. 权限设置

**桌面壁纸**: 无需额外权限

**锁屏壁纸**: 可能需要：
- 应用必须正确打包为 MSIX
- 在 Windows 设置中允许应用修改个性化设置
- 某些企业环境可能有策略限制

## 故障排除

### 常见问题

**Q: 应用无法启动**
A: 
- 检查是否安装了 .NET 8.0 运行时
- 确保 Windows 版本支持 (Windows 10 1903+)
- 尝试重新安装应用

**Q: API 连接失败**
A:
- 检查网络连接
- 验证 API 密钥是否有效
- 尝试使用测试脚本: `.\test-simple.ps1`

**Q: 锁屏壁纸设置失败**
A:
- 确保应用已打包为 MSIX 安装
- 检查 Windows 个性化设置权限
- 某些企业版本可能有组策略限制

**Q: 图片下载缓慢**
A:
- 检查网络速度
- 尝试更换图源
- 调整并发下载数设置

### 日志查看

应用日志位置：
- **开发模式**: Visual Studio 输出窗口
- **命令行模式**: 控制台输出
- **安装版本**: Windows 事件查看器

### 重置应用

如果遇到严重问题，可以重置应用：
1. 在 Windows 设置中找到 "自动壁纸" 应用
2. 点击 **高级选项**
3. 点击 **重置** 按钮

## 卸载应用

### 从 Windows 设置卸载

1. 打开 **Windows 设置** (Win + I)
2. 转到 **应用** > **已安装的应用**
3. 找到 "自动壁纸" 应用
4. 点击 **...** > **卸载**

### 从开始菜单卸载

1. 打开开始菜单
2. 找到 "自动壁纸" 应用
3. 右键点击 > **卸载**

### PowerShell 卸载

```powershell
Get-AppxPackage -Name "*AutoWallpaper*" | Remove-AppxPackage
```

## 技术支持

如果遇到问题：
1. 查看本文档的故障排除部分
2. 运行测试脚本诊断问题
3. 查看应用日志
4. 提交 Issue 到项目仓库

## 更新应用

当有新版本时：
1. 下载新的 MSIX 安装包
2. 双击安装（会自动覆盖旧版本）
3. 或使用 PowerShell: `Add-AppxPackage -Path "new-version.msix"`

应用会保留现有配置和缓存文件。
