# 自动壁纸应用构建脚本

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [switch]$Clean,
    [switch]$Pack,
    [switch]$Install
)

$ErrorActionPreference = "Stop"

Write-Host "=== 自动壁纸应用构建脚本 ===" -ForegroundColor Green
Write-Host "配置: $Configuration" -ForegroundColor Yellow
Write-Host "平台: $Platform" -ForegroundColor Yellow

# 检查必要工具
if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
    Write-Error ".NET SDK 未安装或未添加到 PATH"
    exit 1
}

# 清理
if ($Clean) {
    Write-Host "清理项目..." -ForegroundColor Yellow
    dotnet clean --configuration $Configuration
    if (Test-Path "bin") { Remove-Item "bin" -Recurse -Force }
    if (Test-Path "obj") { Remove-Item "obj" -Recurse -Force }
}

# 还原依赖
Write-Host "还原 NuGet 包..." -ForegroundColor Yellow
dotnet restore

# 构建项目
Write-Host "构建项目..." -ForegroundColor Yellow
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "构建失败"
    exit $LASTEXITCODE
}

# 打包 MSIX
if ($Pack) {
    Write-Host "打包 MSIX..." -ForegroundColor Yellow
    
    # 检查是否有 MSBuild
    $msbuild = Get-Command "msbuild" -ErrorAction SilentlyContinue
    if (-not $msbuild) {
        # 尝试找到 Visual Studio 的 MSBuild
        $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
        if (Test-Path $vsWhere) {
            $vsPath = & $vsWhere -latest -products * -requires Microsoft.Component.MSBuild -property installationPath
            if ($vsPath) {
                $msbuild = "$vsPath\MSBuild\Current\Bin\MSBuild.exe"
            }
        }
    }
    
    if (-not (Test-Path $msbuild)) {
        Write-Error "未找到 MSBuild，请安装 Visual Studio 或 Build Tools"
        exit 1
    }
    
    & $msbuild AutoWallpaper.csproj `
        /p:Configuration=$Configuration `
        /p:Platform=$Platform `
        /p:UapAppxPackageBuildMode=StoreUpload `
        /p:AppxBundlePlatforms=$Platform `
        /p:PackageCertificateKeyFile="" `
        /p:PackageCertificatePassword="" `
        /p:AppxPackageSigningEnabled=false
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "MSIX 打包失败"
        exit $LASTEXITCODE
    }
    
    Write-Host "MSIX 打包完成" -ForegroundColor Green
}

# 安装应用
if ($Install) {
    Write-Host "安装应用..." -ForegroundColor Yellow
    
    $msixPath = Get-ChildItem -Path "bin\$Platform\$Configuration" -Filter "*.msix" -Recurse | Select-Object -First 1
    if ($msixPath) {
        Write-Host "找到安装包: $($msixPath.FullName)" -ForegroundColor Yellow
        
        # 使用 PowerShell 安装 MSIX
        try {
            Add-AppxPackage -Path $msixPath.FullName -ForceApplicationShutdown
            Write-Host "应用安装成功" -ForegroundColor Green
        }
        catch {
            Write-Warning "自动安装失败: $($_.Exception.Message)"
            Write-Host "请手动双击安装包进行安装: $($msixPath.FullName)" -ForegroundColor Yellow
        }
    }
    else {
        Write-Error "未找到 MSIX 安装包，请先运行打包 (-Pack)"
        exit 1
    }
}

Write-Host "构建完成!" -ForegroundColor Green

# 显示输出文件
$outputDir = "bin\$Platform\$Configuration"
if (Test-Path $outputDir) {
    Write-Host "`n输出文件:" -ForegroundColor Yellow
    Get-ChildItem $outputDir -Recurse -File | Where-Object { $_.Extension -in @('.exe', '.msix', '.appx') } | ForEach-Object {
        Write-Host "  $($_.FullName)" -ForegroundColor Cyan
    }
}
