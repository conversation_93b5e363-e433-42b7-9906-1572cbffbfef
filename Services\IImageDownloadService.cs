using AutoWallpaper.Models;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 图片下载服务接口
/// </summary>
public interface IImageDownloadService
{
    /// <summary>
    /// 下载单张图片
    /// </summary>
    Task<string?> DownloadImageAsync(WallpaperImage image, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量下载图片
    /// </summary>
    Task<List<string>> DownloadImagesAsync(IEnumerable<WallpaperImage> images, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查图片是否已下载
    /// </summary>
    Task<bool> IsImageDownloadedAsync(WallpaperImage image);

    /// <summary>
    /// 获取本地图片路径
    /// </summary>
    Task<string?> GetLocalImagePathAsync(WallpaperImage image);

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    Task CleanupCacheAsync();

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    Task<CacheStats> GetCacheStatsAsync();

    /// <summary>
    /// 获取所有本地图片
    /// </summary>
    Task<List<string>> GetLocalImagesAsync();
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStats
{
    public int TotalFiles { get; set; }
    public long TotalSize { get; set; }
    public string FormattedSize => FormatBytes(TotalSize);
    
    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }
}
