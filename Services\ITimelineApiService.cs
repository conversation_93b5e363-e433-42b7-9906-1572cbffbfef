using AutoWallpaper.Models;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 拾光壁纸 API 服务接口
/// </summary>
public interface ITimelineApiService
{
    /// <summary>
    /// 获取随机壁纸
    /// </summary>
    Task<WallpaperImage?> GetRandomImageAsync(string provider = "snake", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取今日壁纸
    /// </summary>
    Task<WallpaperImage?> GetTodayImageAsync(string provider = "snake", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取壁纸列表（分页）
    /// </summary>
    Task<List<WallpaperImage>> GetImagesAsync(
        string provider = "snake",
        string order = "score",
        string categoryHow = "",
        string categoryWhat = "",
        int? no = null,
        string? seed = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取分类信息
    /// </summary>
    Task<List<CategoryInfo>> GetCategoriesAsync(string provider = "snake", string version = "v2", CancellationToken cancellationToken = default);

    /// <summary>
    /// 测试 API 连接
    /// </summary>
    Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 分类信息
/// </summary>
public class CategoryInfo
{
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("blink")]
    public int Blink { get; set; }

    [JsonPropertyName("score")]
    public double Score { get; set; }

    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;
}
