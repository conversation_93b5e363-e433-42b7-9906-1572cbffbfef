<Page x:Class="AutoWallpaper.Views.SchedulePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="24">
            
            <TextBlock Text="定时设置" Style="{StaticResource TitleTextBlockStyle}"/>
            
            <!-- 定时设置功能占位 -->
            <Border Style="{StaticResource CardStyle}" MinHeight="400">
                <StackPanel Spacing="16" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <SymbolIcon Symbol="Clock" FontSize="48" Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="高级定时设置" 
                              Style="{StaticResource SubtitleTextBlockStyle}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="此功能将在后续版本中实现" 
                              Style="{StaticResource BodyTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="• 自定义更换时间段" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="• 工作日/周末不同设置" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                    <TextBlock Text="• 系统空闲检测" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</Page>
