{"version": "0.2.0", "configurations": [{"name": "启动应用 (GUI)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0-windows10.0.19041.0/AutoWallpaper.exe", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "preLaunchTask": "build"}, {"name": "测试 API 连接", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0-windows10.0.19041.0/AutoWallpaper.exe", "args": ["--test-api"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopAtEntry": false, "preLaunchTask": "build"}, {"name": "立即更换壁纸", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0-windows10.0.19041.0/AutoWallpaper.exe", "args": ["--change-now"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopAtEntry": false, "preLaunchTask": "build"}, {"name": "启动定时任务 (30分钟)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0-windows10.0.19041.0/AutoWallpaper.exe", "args": ["--start-schedule", "30"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopAtEntry": false, "preLaunchTask": "build"}]}