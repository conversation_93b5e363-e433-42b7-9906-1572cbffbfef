<Page x:Class="AutoWallpaper.Views.HomePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:AutoWallpaper.Views"
      xmlns:vm="using:AutoWallpaper.ViewModels">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="24">
            
            <!-- 标题区域 -->
            <StackPanel Spacing="8">
                <TextBlock Text="自动壁纸" 
                          Style="{StaticResource TitleTextBlockStyle}"/>
                <TextBlock Text="从拾光壁纸网站自动获取并设置 Windows 11 桌面和锁屏壁纸" 
                          Style="{StaticResource BodyTextBlockStyle}"
                          Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
            </StackPanel>

            <!-- 快速操作区域 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="16">
                    <TextBlock Text="快速操作" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="12">
                        <!-- 立即更换 -->
                        <Button Grid.Column="0" 
                               HorizontalAlignment="Stretch"
                               Command="{x:Bind ViewModel.ChangeWallpaperNowCommand}"
                               IsEnabled="{x:Bind ViewModel.IsChangingWallpaper, Mode=OneWay, Converter={StaticResource BoolNegationConverter}}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <SymbolIcon Symbol="Refresh"/>
                                <TextBlock Text="{x:Bind ViewModel.IsChangingWallpaper, Mode=OneWay, Converter={StaticResource BoolToTextConverter}, ConverterParameter='更换中...|立即更换'}"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- 定时开关 -->
                        <Button Grid.Column="1" 
                               HorizontalAlignment="Stretch"
                               Command="{x:Bind ViewModel.ToggleScheduleCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <SymbolIcon Symbol="{x:Bind ViewModel.IsScheduleRunning, Mode=OneWay, Converter={StaticResource BoolToSymbolConverter}, ConverterParameter='Pause|Play'}"/>
                                <TextBlock Text="{x:Bind ViewModel.IsScheduleRunning, Mode=OneWay, Converter={StaticResource BoolToTextConverter}, ConverterParameter='停止定时|启动定时'}"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 状态信息区域 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="12">
                    <TextBlock Text="状态信息" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <Grid RowDefinitions="Auto,Auto,Auto,Auto" ColumnDefinitions="120,*" RowSpacing="8">
                        <!-- 当前状态 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="当前状态:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{x:Bind ViewModel.StatusMessage, Mode=OneWay}" 
                                  VerticalAlignment="Center" FontWeight="SemiBold"/>
                        
                        <!-- 定时状态 -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="定时任务:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" VerticalAlignment="Center">
                            <Run Text="{x:Bind ViewModel.IsScheduleRunning, Mode=OneWay, Converter={StaticResource BoolToTextConverter}, ConverterParameter='运行中|已停止'}"/>
                            <Run Text="{x:Bind ViewModel.NextChangeTime, Mode=OneWay, Converter={StaticResource DateTimeToStringConverter}, ConverterParameter=' (下次: {0:HH:mm})'}" 
                                 Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                        </TextBlock>
                        
                        <!-- 当前壁纸 -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="当前壁纸:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{x:Bind ViewModel.CurrentImageTitle, Mode=OneWay, FallbackValue='未知'}" 
                                  VerticalAlignment="Center"/>
                        
                        <!-- 缓存统计 -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="本地缓存:" VerticalAlignment="Center"/>
                        <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Spacing="8">
                            <TextBlock VerticalAlignment="Center">
                                <Run Text="{x:Bind ViewModel.CacheStats.TotalFiles, Mode=OneWay}"/>
                                <Run Text="张图片"/>
                                <Run Text="(" Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                                <Run Text="{x:Bind ViewModel.CacheStats.FormattedSize, Mode=OneWay}" Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                                <Run Text=")" Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                            </TextBlock>
                            <Button Content="刷新" 
                                   Command="{x:Bind ViewModel.RefreshCacheStatsCommand}"
                                   Style="{StaticResource AccentButtonStyle}"
                                   Padding="8,4"/>
                            <Button Content="清理" 
                                   Command="{x:Bind ViewModel.CleanupCacheCommand}"
                                   Padding="8,4"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 最近更换记录 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="12">
                    <TextBlock Text="最近更换" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <ListView ItemsSource="{x:Bind ViewModel.RecentChanges}" 
                             MaxHeight="200"
                             SelectionMode="None">
                        <ListView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <TextBlock Text="{x:Bind}" 
                                          Style="{StaticResource CaptionTextBlockStyle}"
                                          Margin="0,4"/>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    
                    <TextBlock Text="暂无记录" 
                              Style="{StaticResource CaptionTextBlockStyle}"
                              Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                              HorizontalAlignment="Center"
                              Visibility="{x:Bind ViewModel.RecentChanges.Count, Mode=OneWay, Converter={StaticResource IntToVisibilityConverter}, ConverterParameter='0'}"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</Page>
