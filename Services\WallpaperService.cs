using AutoWallpaper.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Windows.Storage;
using Windows.System.UserProfile;

namespace AutoWallpaper.Services;

/// <summary>
/// 壁纸设置服务实现
/// </summary>
public class WallpaperService : IWallpaperService
{
    private readonly ILogger<WallpaperService> _logger;
    
    // Win32 API 常量
    private const int SPI_SETDESKWALLPAPER = 0x0014;
    private const int SPIF_UPDATEINIFILE = 0x01;
    private const int SPIF_SENDCHANGE = 0x02;
    
    [DllImport("user32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
    private static extern bool SystemParametersInfoW(
        int uAction, 
        int uParam, 
        string lpvParam, 
        int fuWinIni);

    public WallpaperService(ILogger<WallpaperService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SetDesktopWallpaperAsync(string imagePath, WallpaperStyle style = WallpaperStyle.Fill)
    {
        try
        {
            if (!File.Exists(imagePath))
            {
                _logger.LogError("壁纸文件不存在: {Path}", imagePath);
                return false;
            }

            // 设置壁纸样式
            await SetWallpaperStyleAsync(style);
            
            // 使用 Win32 API 设置桌面壁纸
            var success = SystemParametersInfoW(
                SPI_SETDESKWALLPAPER,
                0,
                imagePath,
                SPIF_UPDATEINIFILE | SPIF_SENDCHANGE);

            if (success)
            {
                _logger.LogInformation("桌面壁纸设置成功: {Path}", imagePath);
                return true;
            }
            else
            {
                var error = Marshal.GetLastWin32Error();
                _logger.LogError("桌面壁纸设置失败: {Error}", error);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置桌面壁纸异常: {Path}", imagePath);
            return false;
        }
    }

    public async Task<bool> SetLockScreenWallpaperAsync(string imagePath)
    {
        try
        {
            if (!File.Exists(imagePath))
            {
                _logger.LogError("锁屏壁纸文件不存在: {Path}", imagePath);
                return false;
            }

            // 检查是否支持锁屏壁纸设置
            if (!UserProfilePersonalizationSettings.IsSupported())
            {
                _logger.LogWarning("当前系统不支持锁屏壁纸设置");
                return false;
            }

            // 使用 Windows App SDK API 设置锁屏壁纸
            var file = await StorageFile.GetFileFromPathAsync(imagePath);
            var settings = UserProfilePersonalizationSettings.Current;
            var success = await settings.TrySetLockScreenImageAsync(file);

            if (success)
            {
                _logger.LogInformation("锁屏壁纸设置成功: {Path}", imagePath);
                return true;
            }
            else
            {
                _logger.LogWarning("锁屏壁纸设置失败，可能由于系统策略限制: {Path}", imagePath);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置锁屏壁纸异常: {Path}", imagePath);
            return false;
        }
    }

    public async Task<WallpaperSetResult> SetBothWallpapersAsync(string imagePath, WallpaperStyle style = WallpaperStyle.Fill)
    {
        var result = new WallpaperSetResult();
        
        // 设置桌面壁纸
        try
        {
            result.DesktopSuccess = await SetDesktopWallpaperAsync(imagePath, style);
        }
        catch (Exception ex)
        {
            result.DesktopError = ex.Message;
            _logger.LogError(ex, "设置桌面壁纸失败");
        }

        // 设置锁屏壁纸
        try
        {
            result.LockScreenSuccess = await SetLockScreenWallpaperAsync(imagePath);
        }
        catch (Exception ex)
        {
            result.LockScreenError = ex.Message;
            _logger.LogError(ex, "设置锁屏壁纸失败");
        }

        return result;
    }

    public async Task<bool> IsLockScreenSupportedAsync()
    {
        return await Task.FromResult(UserProfilePersonalizationSettings.IsSupported());
    }

    public async Task<string?> GetCurrentDesktopWallpaperAsync()
    {
        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Desktop");
            var wallpaper = key?.GetValue("Wallpaper")?.ToString();
            return await Task.FromResult(wallpaper);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前桌面壁纸失败");
            return null;
        }
    }

    public async Task<string?> GetCurrentLockScreenWallpaperAsync()
    {
        // Windows 11 锁屏壁纸路径较复杂，暂时返回 null
        // 可以通过注册表或文件系统查找，但不够可靠
        return await Task.FromResult<string?>(null);
    }

    private async Task SetWallpaperStyleAsync(WallpaperStyle style)
    {
        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(@"Control Panel\Desktop", true);
            if (key != null)
            {
                switch (style)
                {
                    case WallpaperStyle.Fill:
                        key.SetValue("WallpaperStyle", "10");
                        key.SetValue("TileWallpaper", "0");
                        break;
                    case WallpaperStyle.Fit:
                        key.SetValue("WallpaperStyle", "6");
                        key.SetValue("TileWallpaper", "0");
                        break;
                    case WallpaperStyle.Stretch:
                        key.SetValue("WallpaperStyle", "2");
                        key.SetValue("TileWallpaper", "0");
                        break;
                    case WallpaperStyle.Tile:
                        key.SetValue("WallpaperStyle", "0");
                        key.SetValue("TileWallpaper", "1");
                        break;
                    case WallpaperStyle.Center:
                        key.SetValue("WallpaperStyle", "0");
                        key.SetValue("TileWallpaper", "0");
                        break;
                    case WallpaperStyle.Span:
                        key.SetValue("WallpaperStyle", "22");
                        key.SetValue("TileWallpaper", "0");
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "设置壁纸样式失败: {Style}", style);
        }
    }
}
