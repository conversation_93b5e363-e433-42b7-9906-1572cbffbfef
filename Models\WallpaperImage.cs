using System;
using System.Text.Json.Serialization;

namespace AutoWallpaper.Models;

/// <summary>
/// 拾光壁纸图片数据模型
/// </summary>
public class WallpaperImage
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("no")]
    public int No { get; set; }

    [JsonPropertyName("reldate")]
    public string RelDate { get; set; } = string.Empty;

    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("story")]
    public string Story { get; set; } = string.Empty;

    [JsonPropertyName("copyright")]
    public string Copyright { get; set; } = string.Empty;

    [JsonPropertyName("copyrightrich")]
    public string CopyrightRich { get; set; } = string.Empty;

    [JsonPropertyName("cateid")]
    public string CategoryId { get; set; } = string.Empty;

    [JsonPropertyName("catename")]
    public string CategoryName { get; set; } = string.Empty;

    [JsonPropertyName("catehowid")]
    public string CategoryHowId { get; set; } = string.Empty;

    [JsonPropertyName("catehowname")]
    public string CategoryHowName { get; set; } = string.Empty;

    [JsonPropertyName("catewhatid")]
    public string CategoryWhatId { get; set; } = string.Empty;

    [JsonPropertyName("catewhatname")]
    public string CategoryWhatName { get; set; } = string.Empty;

    [JsonPropertyName("imgurl")]
    public string ImageUrl { get; set; } = string.Empty;

    [JsonPropertyName("thumburl")]
    public string ThumbUrl { get; set; } = string.Empty;

    [JsonPropertyName("width")]
    public int Width { get; set; }

    [JsonPropertyName("height")]
    public int Height { get; set; }

    [JsonPropertyName("size")]
    public long Size { get; set; }

    [JsonPropertyName("ext")]
    public string Extension { get; set; } = string.Empty;

    [JsonPropertyName("md5")]
    public string Md5 { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    [JsonPropertyName("score")]
    public double Score { get; set; }

    [JsonPropertyName("rank")]
    public int Rank { get; set; }

    /// <summary>
    /// 获取本地文件名（基于 MD5）
    /// </summary>
    public string GetLocalFileName() => $"{Md5}{Extension}";

    /// <summary>
    /// 获取图片比例
    /// </summary>
    public double AspectRatio => Width > 0 && Height > 0 ? (double)Width / Height : 0;

    /// <summary>
    /// 判断是否为高质量图片（可根据需要调整标准）
    /// </summary>
    public bool IsHighQuality => Width >= 1920 && Height >= 1080 && Size > 100_000;
}

/// <summary>
/// API 响应模型
/// </summary>
public class TimelineApiResponse<T>
{
    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("msg")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("data")]
    public T Data { get; set; } = default!;

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    public bool IsSuccess => Status == 1;
}

/// <summary>
/// 壁纸设置目标
/// </summary>
public enum WallpaperTarget
{
    Desktop,
    LockScreen,
    Both
}

/// <summary>
/// 壁纸样式
/// </summary>
public enum WallpaperStyle
{
    Fill = 10,      // 填充
    Fit = 6,        // 适应
    Stretch = 2,    // 拉伸
    Tile = 0,       // 平铺
    Center = 0,     // 居中
    Span = 22       // 跨越（多显示器）
}
