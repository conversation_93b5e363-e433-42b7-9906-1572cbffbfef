using AutoWallpaper.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AutoWallpaper;

/// <summary>
/// 程序入口点，支持命令行参数
/// </summary>
public class Program
{
    [STAThread]
    public static async Task<int> Main(string[] args)
    {
        // 检查命令行参数
        if (args.Length > 0)
        {
            return await HandleCommandLineAsync(args);
        }

        // 启动 WinUI 应用
        Microsoft.UI.Xaml.Application.Start((p) => new App());
        return 0;
    }

    private static async Task<int> HandleCommandLineAsync(string[] args)
    {
        try
        {
            // 初始化服务（无 UI）
            var services = new ServiceCollection();
            ConfigureServices(services);
            var serviceProvider = services.BuildServiceProvider();

            var command = args[0].ToLowerInvariant();
            
            switch (command)
            {
                case "--change-now":
                case "-c":
                    return await ChangeWallpaperNowAsync(serviceProvider);
                
                case "--start-schedule":
                case "-s":
                    var interval = args.Length > 1 && int.TryParse(args[1], out var minutes) ? minutes : 60;
                    return await StartScheduleAsync(serviceProvider, interval);
                
                case "--stop-schedule":
                case "-x":
                    return await StopScheduleAsync(serviceProvider);
                
                case "--test-api":
                case "-t":
                    return await TestApiAsync(serviceProvider);
                
                case "--help":
                case "-h":
                case "/?":
                    ShowHelp();
                    return 0;
                
                default:
                    Console.WriteLine($"未知命令: {command}");
                    ShowHelp();
                    return 1;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"执行失败: {ex.Message}");
            return 1;
        }
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        services.AddSingleton<ITimelineApiService, TimelineApiService>();
        services.AddSingleton<IImageDownloadService, ImageDownloadService>();
        services.AddSingleton<IWallpaperService, WallpaperService>();
        services.AddSingleton<ISchedulerService, SchedulerService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        
        services.AddHttpClient<ITimelineApiService, TimelineApiService>(client =>
        {
            client.DefaultRequestHeaders.Add("Timeline-Client", "wer7qx");
            client.Timeout = TimeSpan.FromSeconds(30);
        });
        
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });
    }

    private static async Task<int> ChangeWallpaperNowAsync(IServiceProvider serviceProvider)
    {
        Console.WriteLine("正在更换壁纸...");
        
        var scheduler = serviceProvider.GetRequiredService<ISchedulerService>();
        await scheduler.TriggerWallpaperChangeAsync();
        
        Console.WriteLine("壁纸更换完成");
        return 0;
    }

    private static async Task<int> StartScheduleAsync(IServiceProvider serviceProvider, int intervalMinutes)
    {
        if (intervalMinutes < 30)
        {
            Console.WriteLine("错误: 更换间隔不能少于 30 分钟");
            return 1;
        }

        Console.WriteLine($"启动定时任务，间隔: {intervalMinutes} 分钟");
        
        var scheduler = serviceProvider.GetRequiredService<ISchedulerService>();
        await scheduler.StartScheduleAsync(intervalMinutes);
        
        Console.WriteLine("定时任务已启动，按任意键停止...");
        Console.ReadKey();
        
        await scheduler.StopScheduleAsync();
        Console.WriteLine("定时任务已停止");
        return 0;
    }

    private static async Task<int> StopScheduleAsync(IServiceProvider serviceProvider)
    {
        Console.WriteLine("停止定时任务...");
        
        var scheduler = serviceProvider.GetRequiredService<ISchedulerService>();
        await scheduler.StopScheduleAsync();
        
        Console.WriteLine("定时任务已停止");
        return 0;
    }

    private static async Task<int> TestApiAsync(IServiceProvider serviceProvider)
    {
        Console.WriteLine("测试 API 连接...");
        
        var apiService = serviceProvider.GetRequiredService<ITimelineApiService>();
        var success = await apiService.TestConnectionAsync();
        
        Console.WriteLine(success ? "API 连接成功" : "API 连接失败");
        return success ? 0 : 1;
    }

    private static void ShowHelp()
    {
        Console.WriteLine("自动壁纸 - Windows 11 壁纸自动更换应用");
        Console.WriteLine();
        Console.WriteLine("用法:");
        Console.WriteLine("  AutoWallpaper.exe [选项]");
        Console.WriteLine();
        Console.WriteLine("选项:");
        Console.WriteLine("  -c, --change-now              立即更换壁纸");
        Console.WriteLine("  -s, --start-schedule [间隔]    启动定时任务（分钟，默认 60）");
        Console.WriteLine("  -x, --stop-schedule           停止定时任务");
        Console.WriteLine("  -t, --test-api                测试 API 连接");
        Console.WriteLine("  -h, --help                    显示此帮助信息");
        Console.WriteLine();
        Console.WriteLine("示例:");
        Console.WriteLine("  AutoWallpaper.exe -c                    # 立即更换壁纸");
        Console.WriteLine("  AutoWallpaper.exe -s 30                 # 每 30 分钟更换一次");
        Console.WriteLine("  AutoWallpaper.exe -t                    # 测试 API");
        Console.WriteLine();
        Console.WriteLine("无参数启动将打开图形界面。");
    }
}
