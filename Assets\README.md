# Assets 目录

此目录包含应用程序的图标和图像资源。

## 需要的文件

请添加以下图标文件到此目录：

- `icon.ico` - 应用程序图标（16x16, 32x32, 48x48, 256x256）
- `SplashScreen.scale-200.png` - 启动屏幕 (1240x600)
- `LockScreenLogo.scale-200.png` - 锁屏徽标 (48x48)
- `Square150x150Logo.scale-200.png` - 中等磁贴 (300x300)
- `Square44x44Logo.scale-200.png` - 小磁贴和任务栏 (88x88)
- `Square44x44Logo.targetsize-24_altform-unplated.png` - 无背景小图标 (24x24)
- `StoreLogo.png` - 应用商店徽标 (50x50)
- `Wide310x150Logo.scale-200.png` - 宽磁贴 (620x300)

## 图标设计建议

- 使用简洁的壁纸/图片相关图标
- 主色调建议使用蓝紫色系 (#6B73FF)
- 确保在浅色和深色背景下都清晰可见
- 遵循 Windows 11 设计语言（圆角、简洁）

## 临时解决方案

如果暂时没有设计图标，可以：
1. 使用 Visual Studio 的默认图标模板
2. 从 Windows 图标库复制相似图标
3. 使用在线图标生成器创建基础图标
