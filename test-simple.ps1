# 简化的 API 测试脚本

Write-Host "=== 拾光壁纸 API 测试 ===" -ForegroundColor Green

try {
    Write-Host "测试 API 连接..." -ForegroundColor Yellow
    
    $headers = @{ "Timeline-Client" = "wer7qx" }
    $uri = "https://api.nguaduot.cn/timeline/random?json=1"
    
    Write-Host "请求地址: $uri" -ForegroundColor Cyan
    
    $response = Invoke-RestMethod -Uri $uri -Headers $headers -TimeoutSec 10
    
    if ($response.status -eq 1 -and $response.data) {
        Write-Host "✓ API 连接成功" -ForegroundColor Green
        Write-Host "图片信息:" -ForegroundColor Yellow
        Write-Host "  标题: $($response.data.title)" -ForegroundColor Cyan
        Write-Host "  尺寸: $($response.data.width)x$($response.data.height)" -ForegroundColor Cyan
        Write-Host "  大小: $([math]::Round($response.data.size / 1024 / 1024, 2)) MB" -ForegroundColor Cyan
        Write-Host "  链接: $($response.data.imgurl)" -ForegroundColor Cyan
    }
    else {
        Write-Host "✗ API 返回异常: $($response.msg)" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ API 连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成!" -ForegroundColor Green
