using AutoWallpaper.Models;
using AutoWallpaper.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace AutoWallpaper.ViewModels;

/// <summary>
/// 设置页面 ViewModel
/// </summary>
public partial class SettingsViewModel : ObservableObject
{
    private readonly IConfigurationService _configService;
    private readonly ITimelineApiService _apiService;
    private readonly IWallpaperService _wallpaperService;
    private readonly ILogger<SettingsViewModel> _logger;

    [ObservableProperty]
    private AppConfig _config = new();

    [ObservableProperty]
    private bool _isTestingConnection;

    [ObservableProperty]
    private string _connectionStatus = "未测试";

    [ObservableProperty]
    private bool _isLockScreenSupported;

    [ObservableProperty]
    private bool _isSaving;

    public ObservableCollection<ProviderOption> ProviderOptions { get; } = new();
    public ObservableCollection<OrderOption> OrderOptions { get; } = new();
    public ObservableCollection<CategoryOption> CategoryHowOptions { get; } = new();
    public ObservableCollection<CategoryOption> CategoryWhatOptions { get; } = new();
    public ObservableCollection<WallpaperTargetOption> TargetOptions { get; } = new();
    public ObservableCollection<WallpaperStyleOption> StyleOptions { get; } = new();

    public SettingsViewModel(
        IConfigurationService configService,
        ITimelineApiService apiService,
        IWallpaperService wallpaperService,
        ILogger<SettingsViewModel> logger)
    {
        _configService = configService;
        _apiService = apiService;
        _wallpaperService = wallpaperService;
        _logger = logger;

        InitializeOptions();
    }

    [RelayCommand]
    private async Task InitializeAsync()
    {
        try
        {
            Config = await _configService.GetConfigAsync();
            IsLockScreenSupported = await _wallpaperService.IsLockScreenSupportedAsync();
            
            // 加载分类选项
            await LoadCategoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置页面初始化失败");
        }
    }

    [RelayCommand]
    private async Task SaveConfigAsync()
    {
        if (IsSaving) return;

        try
        {
            IsSaving = true;
            await _configService.SaveConfigAsync(Config);
            _logger.LogInformation("配置保存成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置失败");
        }
        finally
        {
            IsSaving = false;
        }
    }

    [RelayCommand]
    private async Task TestConnectionAsync()
    {
        if (IsTestingConnection) return;

        try
        {
            IsTestingConnection = true;
            ConnectionStatus = "测试中...";
            
            var success = await _apiService.TestConnectionAsync();
            ConnectionStatus = success ? "连接成功" : "连接失败";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试连接失败");
            ConnectionStatus = "测试异常";
        }
        finally
        {
            IsTestingConnection = false;
        }
    }

    [RelayCommand]
    private async Task ResetToDefaultAsync()
    {
        try
        {
            await _configService.ResetToDefaultAsync();
            Config = await _configService.GetConfigAsync();
            _logger.LogInformation("配置已重置");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置配置失败");
        }
    }

    private void InitializeOptions()
    {
        // 图源选项
        ProviderOptions.Clear();
        ProviderOptions.Add(new ProviderOption("snake", "故纸堆（聚合）", "所有图源的聚合版"));
        ProviderOptions.Add(new ProviderOption("glutton", "周度精选", "拾光官方维护，周度精选"));
        ProviderOptions.Add(new ProviderOption("timeline", "拾光", "拾光官方维护，每日一图"));
        ProviderOptions.Add(new ProviderOption("bing", "必应", "必应每日壁纸"));
        ProviderOptions.Add(new ProviderOption("wallhaven", "WallHaven", "高质量壁纸社区"));
        ProviderOptions.Add(new ProviderOption("unsplash", "Unsplash", "免费高分辨率图片"));

        // 排序选项
        OrderOptions.Clear();
        OrderOptions.Add(new OrderOption("score", "趋势", "按热度排序"));
        OrderOptions.Add(new OrderOption("date", "最新", "按时间排序"));
        OrderOptions.Add(new OrderOption("random", "随缘", "随机排序"));

        // 壁纸目标选项
        TargetOptions.Clear();
        TargetOptions.Add(new WallpaperTargetOption(WallpaperTarget.Desktop, "桌面壁纸", "仅设置桌面壁纸"));
        TargetOptions.Add(new WallpaperTargetOption(WallpaperTarget.LockScreen, "锁屏壁纸", "仅设置锁屏壁纸"));
        TargetOptions.Add(new WallpaperTargetOption(WallpaperTarget.Both, "桌面+锁屏", "同时设置桌面和锁屏壁纸"));

        // 壁纸样式选项
        StyleOptions.Clear();
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Fill, "填充", "保持比例填充屏幕"));
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Fit, "适应", "完整显示图片"));
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Stretch, "拉伸", "拉伸填满屏幕"));
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Center, "居中", "居中显示"));
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Tile, "平铺", "平铺显示"));
        StyleOptions.Add(new WallpaperStyleOption(WallpaperStyle.Span, "跨越", "跨越多显示器"));
    }

    private async Task LoadCategoriesAsync()
    {
        try
        {
            var categories = await _apiService.GetCategoriesAsync(Config.Api.Provider);
            
            // 大类选项
            CategoryHowOptions.Clear();
            CategoryHowOptions.Add(new CategoryOption("", "全部", "所有大类"));
            foreach (var cat in categories.Where(c => c.Version == "how"))
            {
                CategoryHowOptions.Add(new CategoryOption(cat.Id, cat.Name, $"{cat.Count} 张图片"));
            }

            // 子类选项
            CategoryWhatOptions.Clear();
            CategoryWhatOptions.Add(new CategoryOption("", "全部", "所有子类"));
            foreach (var cat in categories.Where(c => c.Version == "what"))
            {
                CategoryWhatOptions.Add(new CategoryOption(cat.Id, cat.Name, $"{cat.Count} 张图片"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载分类失败");
        }
    }
}

// 选项类
public record ProviderOption(string Id, string Name, string Description);
public record OrderOption(string Id, string Name, string Description);
public record CategoryOption(string Id, string Name, string Description);
public record WallpaperTargetOption(WallpaperTarget Target, string Name, string Description);
public record WallpaperStyleOption(WallpaperStyle Style, string Name, string Description);
