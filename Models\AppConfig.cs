using System;
using System.IO;
using System.Text.Json.Serialization;

namespace AutoWallpaper.Models;

/// <summary>
/// 应用配置模型
/// </summary>
public class AppConfig
{
    /// <summary>
    /// API 配置
    /// </summary>
    public ApiConfig Api { get; set; } = new();

    /// <summary>
    /// 下载配置
    /// </summary>
    public DownloadConfig Download { get; set; } = new();

    /// <summary>
    /// 壁纸配置
    /// </summary>
    public WallpaperConfig Wallpaper { get; set; } = new();

    /// <summary>
    /// 调度配置
    /// </summary>
    public ScheduleConfig Schedule { get; set; } = new();
}

public class ApiConfig
{
    /// <summary>
    /// Timeline API 密钥
    /// </summary>
    public string ApiKey { get; set; } = "wer7qx"; // 默认测试密钥

    /// <summary>
    /// 图源 ID
    /// </summary>
    public string Provider { get; set; } = "snake"; // 聚合图源

    /// <summary>
    /// 排序方式：date(最新)、score(趋势)、random(随缘)
    /// </summary>
    public string Order { get; set; } = "score";

    /// <summary>
    /// 大类过滤：photography(摄影)、general(其他)、空(全部)
    /// </summary>
    public string CategoryHow { get; set; } = "";

    /// <summary>
    /// 子类过滤：landscape(风光)、girl(美女)、character(人物)、living(生灵)、general(其他)、空(全部)
    /// </summary>
    public string CategoryWhat { get; set; } = "";

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;
}

public class DownloadConfig
{
    /// <summary>
    /// 下载目录
    /// </summary>
    public string DownloadPath { get; set; } = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.MyPictures), 
        "AutoWallpaper");

    /// <summary>
    /// 最大缓存图片数量
    /// </summary>
    public int MaxCacheCount { get; set; } = 500;

    /// <summary>
    /// 最小图片宽度
    /// </summary>
    public int MinWidth { get; set; } = 1920;

    /// <summary>
    /// 最小图片高度
    /// </summary>
    public int MinHeight { get; set; } = 1080;

    /// <summary>
    /// 最小文件大小（字节）
    /// </summary>
    public long MinFileSize { get; set; } = 100_000;

    /// <summary>
    /// 并发下载数
    /// </summary>
    public int ConcurrentDownloads { get; set; } = 3;
}

public class WallpaperConfig
{
    /// <summary>
    /// 壁纸设置目标
    /// </summary>
    public WallpaperTarget Target { get; set; } = WallpaperTarget.Both;

    /// <summary>
    /// 桌面壁纸样式
    /// </summary>
    public WallpaperStyle DesktopStyle { get; set; } = WallpaperStyle.Fill;

    /// <summary>
    /// 是否启用桌面壁纸更换
    /// </summary>
    public bool EnableDesktop { get; set; } = true;

    /// <summary>
    /// 是否启用锁屏壁纸更换
    /// </summary>
    public bool EnableLockScreen { get; set; } = true;

    /// <summary>
    /// 是否保留历史壁纸
    /// </summary>
    public bool KeepHistory { get; set; } = true;
}

public class ScheduleConfig
{
    /// <summary>
    /// 是否启用自动更换
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// 更换间隔（分钟），最小值 30
    /// </summary>
    public int IntervalMinutes { get; set; } = 60;

    /// <summary>
    /// 下次更换时间
    /// </summary>
    public DateTime? NextChangeTime { get; set; }

    /// <summary>
    /// 是否仅在空闲时更换
    /// </summary>
    public bool OnlyWhenIdle { get; set; } = false;

    /// <summary>
    /// 验证并修正间隔时间
    /// </summary>
    public void ValidateInterval()
    {
        if (IntervalMinutes < 30)
            IntervalMinutes = 30;
    }
}
