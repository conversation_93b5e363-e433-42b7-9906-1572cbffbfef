using AutoWallpaper.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;

namespace AutoWallpaper.Views;

public sealed partial class HomePage : Page
{
    public MainViewModel ViewModel { get; }

    public HomePage()
    {
        this.InitializeComponent();
        ViewModel = App.Host.Services.GetRequiredService<MainViewModel>();
    }

    protected override async void OnNavigatedTo(NavigationEventArgs e)
    {
        base.OnNavigatedTo(e);
        await ViewModel.InitializeCommand.ExecuteAsync(null);
    }
}
