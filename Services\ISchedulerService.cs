using System;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 调度服务接口
/// </summary>
public interface ISchedulerService
{
    /// <summary>
    /// 启动定时任务
    /// </summary>
    Task StartScheduleAsync(int intervalMinutes);

    /// <summary>
    /// 停止定时任务
    /// </summary>
    Task StopScheduleAsync();

    /// <summary>
    /// 检查定时任务是否运行中
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 获取下次执行时间
    /// </summary>
    DateTime? NextExecutionTime { get; }

    /// <summary>
    /// 手动触发壁纸更换
    /// </summary>
    Task TriggerWallpaperChangeAsync();

    /// <summary>
    /// 定时任务状态变化事件
    /// </summary>
    event EventHandler<ScheduleStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 壁纸更换完成事件
    /// </summary>
    event EventHandler<WallpaperChangedEventArgs>? WallpaperChanged;
}

/// <summary>
/// 调度状态变化事件参数
/// </summary>
public class ScheduleStatusChangedEventArgs : EventArgs
{
    public bool IsRunning { get; set; }
    public DateTime? NextExecutionTime { get; set; }
    public string? Message { get; set; }
}

/// <summary>
/// 壁纸更换事件参数
/// </summary>
public class WallpaperChangedEventArgs : EventArgs
{
    public bool Success { get; set; }
    public string? ImagePath { get; set; }
    public string? ImageTitle { get; set; }
    public string? Error { get; set; }
    public DateTime ChangeTime { get; set; } = DateTime.Now;
}
