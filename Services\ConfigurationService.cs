using AutoWallpaper.Models;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Windows.Storage;

namespace AutoWallpaper.Services;

/// <summary>
/// 配置服务实现
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private AppConfig? _cachedConfig;
    private readonly object _lock = new();
    
    private const string ConfigFileName = "config.json";

    public event EventHandler<ConfigChangedEventArgs>? ConfigChanged;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
    }

    public async Task<AppConfig> GetConfigAsync()
    {
        lock (_lock)
        {
            if (_cachedConfig != null)
                return _cachedConfig;
        }

        try
        {
            var configPath = await GetConfigFilePathAsync();
            
            if (File.Exists(configPath))
            {
                var json = await File.ReadAllTextAsync(configPath);
                var config = JsonSerializer.Deserialize<AppConfig>(json) ?? new AppConfig();
                
                // 验证和修正配置
                ValidateConfig(config);
                
                lock (_lock)
                {
                    _cachedConfig = config;
                }
                
                _logger.LogInformation("配置加载成功: {Path}", configPath);
                return config;
            }
            else
            {
                // 创建默认配置
                var defaultConfig = new AppConfig();
                ValidateConfig(defaultConfig);
                await SaveConfigAsync(defaultConfig);
                
                lock (_lock)
                {
                    _cachedConfig = defaultConfig;
                }
                
                _logger.LogInformation("创建默认配置: {Path}", configPath);
                return defaultConfig;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败，使用默认配置");
            var defaultConfig = new AppConfig();
            ValidateConfig(defaultConfig);
            
            lock (_lock)
            {
                _cachedConfig = defaultConfig;
            }
            
            return defaultConfig;
        }
    }

    public async Task SaveConfigAsync(AppConfig config)
    {
        try
        {
            ValidateConfig(config);
            
            var configPath = await GetConfigFilePathAsync();
            var directory = Path.GetDirectoryName(configPath);
            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var json = JsonSerializer.Serialize(config, options);
            await File.WriteAllTextAsync(configPath, json);
            
            lock (_lock)
            {
                _cachedConfig = config;
            }
            
            _logger.LogInformation("配置保存成功: {Path}", configPath);
            
            ConfigChanged?.Invoke(this, new ConfigChangedEventArgs { Config = config });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置失败");
            throw;
        }
    }

    public async Task ResetToDefaultAsync()
    {
        var defaultConfig = new AppConfig();
        ValidateConfig(defaultConfig);
        await SaveConfigAsync(defaultConfig);
        
        _logger.LogInformation("配置已重置为默认值");
    }

    private static async Task<string> GetConfigFilePathAsync()
    {
        try
        {
            var localFolder = ApplicationData.Current.LocalFolder;
            return Path.Combine(localFolder.Path, ConfigFileName);
        }
        catch
        {
            // 回退到用户文档目录
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var appFolder = Path.Combine(documentsPath, "AutoWallpaper");
            Directory.CreateDirectory(appFolder);
            return Path.Combine(appFolder, ConfigFileName);
        }
    }

    private static void ValidateConfig(AppConfig config)
    {
        // 验证 API 配置
        if (string.IsNullOrEmpty(config.Api.ApiKey))
            config.Api.ApiKey = "wer7qx";
        
        if (string.IsNullOrEmpty(config.Api.Provider))
            config.Api.Provider = "snake";
        
        if (string.IsNullOrEmpty(config.Api.Order))
            config.Api.Order = "score";
        
        if (config.Api.TimeoutSeconds <= 0)
            config.Api.TimeoutSeconds = 30;
        
        if (config.Api.RetryCount < 0)
            config.Api.RetryCount = 3;

        // 验证下载配置
        if (string.IsNullOrEmpty(config.Download.DownloadPath))
        {
            config.Download.DownloadPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyPictures),
                "AutoWallpaper");
        }
        
        if (config.Download.MaxCacheCount <= 0)
            config.Download.MaxCacheCount = 500;
        
        if (config.Download.MinWidth <= 0)
            config.Download.MinWidth = 1920;
        
        if (config.Download.MinHeight <= 0)
            config.Download.MinHeight = 1080;
        
        if (config.Download.MinFileSize <= 0)
            config.Download.MinFileSize = 100_000;
        
        if (config.Download.ConcurrentDownloads <= 0)
            config.Download.ConcurrentDownloads = 3;

        // 验证调度配置
        config.Schedule.ValidateInterval();
    }
}
