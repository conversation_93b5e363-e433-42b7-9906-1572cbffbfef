using AutoWallpaper.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace AutoWallpaper.Services;

/// <summary>
/// 图片下载服务实现
/// </summary>
public class ImageDownloadService : IImageDownloadService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ImageDownloadService> _logger;
    private readonly IConfigurationService _configService;
    private readonly SemaphoreSlim _downloadSemaphore;
    
    private const string StateFileName = "download_state.json";
    
    public ImageDownloadService(
        HttpClient httpClient,
        ILogger<ImageDownloadService> logger,
        IConfigurationService configService)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configService = configService;
        _downloadSemaphore = new SemaphoreSlim(3, 3); // 默认并发数
    }

    public async Task<string?> DownloadImageAsync(WallpaperImage image, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(image.ImageUrl) || string.IsNullOrEmpty(image.Md5))
        {
            _logger.LogWarning("图片信息不完整: {Id}", image.Id);
            return null;
        }

        var config = await _configService.GetConfigAsync();
        var downloadPath = config.Download.DownloadPath;
        
        // 确保下载目录存在
        Directory.CreateDirectory(downloadPath);
        
        var fileName = image.GetLocalFileName();
        var filePath = Path.Combine(downloadPath, fileName);
        
        // 检查文件是否已存在且完整
        if (await IsFileValidAsync(filePath, image.Md5))
        {
            _logger.LogDebug("图片已存在: {FileName}", fileName);
            await UpdateDownloadStateAsync(image);
            return filePath;
        }

        await _downloadSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("开始下载图片: {Title} ({FileName})", image.Title, fileName);
            
            // 下载图片
            using var response = await _httpClient.GetAsync(image.ImageUrl, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var tempPath = filePath + ".tmp";
            await using var fileStream = new FileStream(tempPath, FileMode.Create, FileAccess.Write);
            await response.Content.CopyToAsync(fileStream, cancellationToken);
            await fileStream.FlushAsync(cancellationToken);
            
            // 验证文件完整性
            if (await IsFileValidAsync(tempPath, image.Md5))
            {
                // 移动到最终位置
                if (File.Exists(filePath))
                    File.Delete(filePath);
                File.Move(tempPath, filePath);
                
                await UpdateDownloadStateAsync(image);
                _logger.LogInformation("图片下载完成: {FileName}", fileName);
                return filePath;
            }
            else
            {
                File.Delete(tempPath);
                _logger.LogError("图片校验失败: {FileName}", fileName);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载图片失败: {FileName}", fileName);
            return null;
        }
        finally
        {
            _downloadSemaphore.Release();
        }
    }

    public async Task<List<string>> DownloadImagesAsync(IEnumerable<WallpaperImage> images, CancellationToken cancellationToken = default)
    {
        var tasks = images.Select(img => DownloadImageAsync(img, cancellationToken));
        var results = await Task.WhenAll(tasks);
        return results.Where(path => !string.IsNullOrEmpty(path)).ToList()!;
    }

    public async Task<bool> IsImageDownloadedAsync(WallpaperImage image)
    {
        var config = await _configService.GetConfigAsync();
        var filePath = Path.Combine(config.Download.DownloadPath, image.GetLocalFileName());
        return await IsFileValidAsync(filePath, image.Md5);
    }

    public async Task<string?> GetLocalImagePathAsync(WallpaperImage image)
    {
        if (await IsImageDownloadedAsync(image))
        {
            var config = await _configService.GetConfigAsync();
            return Path.Combine(config.Download.DownloadPath, image.GetLocalFileName());
        }
        return null;
    }

    public async Task CleanupCacheAsync()
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var downloadPath = config.Download.DownloadPath;
            
            if (!Directory.Exists(downloadPath))
                return;

            var files = Directory.GetFiles(downloadPath, "*.*")
                .Where(f => IsImageFile(f))
                .Select(f => new FileInfo(f))
                .OrderBy(f => f.LastAccessTime)
                .ToList();

            if (files.Count <= config.Download.MaxCacheCount)
                return;

            var filesToDelete = files.Take(files.Count - config.Download.MaxCacheCount);
            foreach (var file in filesToDelete)
            {
                try
                {
                    file.Delete();
                    _logger.LogDebug("删除缓存文件: {FileName}", file.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除缓存文件失败: {FileName}", file.Name);
                }
            }
            
            _logger.LogInformation("缓存清理完成，删除 {Count} 个文件", filesToDelete.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存清理失败");
        }
    }

    public async Task<CacheStats> GetCacheStatsAsync()
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var downloadPath = config.Download.DownloadPath;
            
            if (!Directory.Exists(downloadPath))
                return new CacheStats();

            var files = Directory.GetFiles(downloadPath, "*.*")
                .Where(IsImageFile)
                .Select(f => new FileInfo(f))
                .ToList();

            return new CacheStats
            {
                TotalFiles = files.Count,
                TotalSize = files.Sum(f => f.Length)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存统计失败");
            return new CacheStats();
        }
    }

    public async Task<List<string>> GetLocalImagesAsync()
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var downloadPath = config.Download.DownloadPath;
            
            if (!Directory.Exists(downloadPath))
                return new List<string>();

            return Directory.GetFiles(downloadPath, "*.*")
                .Where(IsImageFile)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取本地图片列表失败");
            return new List<string>();
        }
    }

    private static async Task<bool> IsFileValidAsync(string filePath, string expectedMd5)
    {
        if (!File.Exists(filePath))
            return false;

        try
        {
            using var md5 = MD5.Create();
            await using var stream = File.OpenRead(filePath);
            var hash = await md5.ComputeHashAsync(stream);
            var actualMd5 = Convert.ToHexString(hash).ToLowerInvariant();
            return actualMd5 == expectedMd5.ToLowerInvariant();
        }
        catch
        {
            return false;
        }
    }

    private static bool IsImageFile(string filePath)
    {
        var ext = Path.GetExtension(filePath).ToLowerInvariant();
        return ext is ".jpg" or ".jpeg" or ".png" or ".bmp" or ".webp";
    }

    private async Task UpdateDownloadStateAsync(WallpaperImage image)
    {
        try
        {
            var config = await _configService.GetConfigAsync();
            var stateFilePath = Path.Combine(config.Download.DownloadPath, StateFileName);
            
            var state = new DownloadState();
            if (File.Exists(stateFilePath))
            {
                var json = await File.ReadAllTextAsync(stateFilePath);
                state = JsonSerializer.Deserialize<DownloadState>(json) ?? new DownloadState();
            }
            
            state.DownloadedImages[image.Md5] = new DownloadRecord
            {
                Id = image.Id,
                Md5 = image.Md5,
                FileName = image.GetLocalFileName(),
                DownloadTime = DateTime.Now,
                Title = image.Title
            };
            
            var updatedJson = JsonSerializer.Serialize(state, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(stateFilePath, updatedJson);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新下载状态失败");
        }
    }
}

/// <summary>
/// 下载状态
/// </summary>
public class DownloadState
{
    public Dictionary<string, DownloadRecord> DownloadedImages { get; set; } = new();
}

/// <summary>
/// 下载记录
/// </summary>
public class DownloadRecord
{
    public string Id { get; set; } = string.Empty;
    public string Md5 { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public DateTime DownloadTime { get; set; }
    public string Title { get; set; } = string.Empty;
}
