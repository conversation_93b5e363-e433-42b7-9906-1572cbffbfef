<Page x:Class="AutoWallpaper.Views.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:AutoWallpaper.Views"
      xmlns:vm="using:AutoWallpaper.ViewModels">

    <ScrollViewer>
        <StackPanel Spacing="16" Margin="24">
            
            <!-- 标题 -->
            <TextBlock Text="设置" Style="{StaticResource TitleTextBlockStyle}"/>

            <!-- API 配置 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="16">
                    <TextBlock Text="API 配置" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto" ColumnDefinitions="120,*,Auto" RowSpacing="12" ColumnSpacing="12">
                        <!-- API 密钥 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="API 密钥:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" 
                                Text="{x:Bind ViewModel.Config.Api.ApiKey, Mode=TwoWay}"
                                PlaceholderText="请输入 Timeline API 密钥"/>
                        <Button Grid.Row="0" Grid.Column="2" 
                               Content="测试"
                               Command="{x:Bind ViewModel.TestConnectionCommand}"
                               IsEnabled="{x:Bind ViewModel.IsTestingConnection, Mode=OneWay, Converter={StaticResource BoolNegationConverter}}"/>
                        
                        <!-- 连接状态 -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="连接状态:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                                  Text="{x:Bind ViewModel.ConnectionStatus, Mode=OneWay}" 
                                  VerticalAlignment="Center"/>
                        
                        <!-- 图源选择 -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="图源:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2"
                                 ItemsSource="{x:Bind ViewModel.ProviderOptions}"
                                 SelectedValue="{x:Bind ViewModel.Config.Api.Provider, Mode=TwoWay}"
                                 SelectedValuePath="Id"
                                 DisplayMemberPath="Name"
                                 HorizontalAlignment="Stretch"/>
                        
                        <!-- 排序方式 -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="排序:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2"
                                 ItemsSource="{x:Bind ViewModel.OrderOptions}"
                                 SelectedValue="{x:Bind ViewModel.Config.Api.Order, Mode=TwoWay}"
                                 SelectedValuePath="Id"
                                 DisplayMemberPath="Name"
                                 HorizontalAlignment="Stretch"/>
                        
                        <!-- 分类过滤 -->
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="分类:" VerticalAlignment="Center"/>
                        <StackPanel Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="2" Spacing="8">
                            <ComboBox ItemsSource="{x:Bind ViewModel.CategoryHowOptions}"
                                     SelectedValue="{x:Bind ViewModel.Config.Api.CategoryHow, Mode=TwoWay}"
                                     SelectedValuePath="Id"
                                     DisplayMemberPath="Name"
                                     PlaceholderText="选择大类"
                                     HorizontalAlignment="Stretch"/>
                            <ComboBox ItemsSource="{x:Bind ViewModel.CategoryWhatOptions}"
                                     SelectedValue="{x:Bind ViewModel.Config.Api.CategoryWhat, Mode=TwoWay}"
                                     SelectedValuePath="Id"
                                     DisplayMemberPath="Name"
                                     PlaceholderText="选择子类"
                                     HorizontalAlignment="Stretch"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 壁纸配置 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="16">
                    <TextBlock Text="壁纸配置" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <Grid RowDefinitions="Auto,Auto,Auto,Auto" ColumnDefinitions="120,*" RowSpacing="12" ColumnSpacing="12">
                        <!-- 设置目标 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="设置目标:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="0" Grid.Column="1"
                                 ItemsSource="{x:Bind ViewModel.TargetOptions}"
                                 SelectedValue="{x:Bind ViewModel.Config.Wallpaper.Target, Mode=TwoWay}"
                                 SelectedValuePath="Target"
                                 DisplayMemberPath="Name"
                                 HorizontalAlignment="Stretch"/>
                        
                        <!-- 桌面样式 -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="桌面样式:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="1" Grid.Column="1"
                                 ItemsSource="{x:Bind ViewModel.StyleOptions}"
                                 SelectedValue="{x:Bind ViewModel.Config.Wallpaper.DesktopStyle, Mode=TwoWay}"
                                 SelectedValuePath="Style"
                                 DisplayMemberPath="Name"
                                 HorizontalAlignment="Stretch"/>
                        
                        <!-- 锁屏支持状态 -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="锁屏支持:" VerticalAlignment="Center"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Spacing="8">
                            <FontIcon Glyph="{x:Bind ViewModel.IsLockScreenSupported, Mode=OneWay, Converter={StaticResource BoolToGlyphConverter}, ConverterParameter='&#xE73E;|&#xE711;'}"
                                     Foreground="{x:Bind ViewModel.IsLockScreenSupported, Mode=OneWay, Converter={StaticResource BoolToBrushConverter}}"/>
                            <TextBlock Text="{x:Bind ViewModel.IsLockScreenSupported, Mode=OneWay, Converter={StaticResource BoolToTextConverter}, ConverterParameter='支持|不支持'}" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <!-- 下载路径 -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="下载路径:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="3" Grid.Column="1" 
                                Text="{x:Bind ViewModel.Config.Download.DownloadPath, Mode=TwoWay}"
                                IsReadOnly="True"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 定时配置 -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Spacing="16">
                    <TextBlock Text="定时配置" Style="{StaticResource SubtitleTextBlockStyle}"/>
                    
                    <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="120,*" RowSpacing="12" ColumnSpacing="12">
                        <!-- 更换间隔 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="更换间隔:" VerticalAlignment="Center"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Spacing="8">
                            <NumberBox Value="{x:Bind ViewModel.Config.Schedule.IntervalMinutes, Mode=TwoWay}"
                                      Minimum="30"
                                      Maximum="10080"
                                      Width="120"/>
                            <TextBlock Text="分钟" VerticalAlignment="Center"/>
                            <TextBlock Text="(最小 30 分钟)" 
                                      VerticalAlignment="Center"
                                      Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                      Style="{StaticResource CaptionTextBlockStyle}"/>
                        </StackPanel>
                        
                        <!-- 空闲时更换 -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="空闲时更换:" VerticalAlignment="Center"/>
                        <ToggleSwitch Grid.Row="1" Grid.Column="1" 
                                     IsOn="{x:Bind ViewModel.Config.Schedule.OnlyWhenIdle, Mode=TwoWay}"
                                     OnContent="是" OffContent="否"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 操作按钮 -->
            <StackPanel Orientation="Horizontal" Spacing="12" HorizontalAlignment="Right">
                <Button Content="重置为默认" 
                       Command="{x:Bind ViewModel.ResetToDefaultCommand}"/>
                <Button Content="保存设置" 
                       Command="{x:Bind ViewModel.SaveConfigCommand}"
                       Style="{StaticResource AccentButtonStyle}"
                       IsEnabled="{x:Bind ViewModel.IsSaving, Mode=OneWay, Converter={StaticResource BoolNegationConverter}}"/>
            </StackPanel>

        </StackPanel>
    </ScrollViewer>
</Page>
