{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Timeline": {"BaseUrl": "https://api.nguaduot.cn", "DefaultApiKey": "wer7qx", "DefaultProvider": "snake", "RequestTimeout": 30, "MaxRetries": 3}, "Download": {"DefaultPath": "%USERPROFILE%\\Pictures\\AutoWallpaper", "MaxCacheSize": 500, "MinImageWidth": 1920, "MinImageHeight": 1080, "MinFileSize": 100000, "ConcurrentDownloads": 3}, "Schedule": {"MinIntervalMinutes": 30, "DefaultIntervalMinutes": 60}}